#!/usr/bin/env python3
"""
优化后的DQN智能体 - 修复索引越界和准确率问题
"""

import torch
import torch.optim as optim
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import random
from models.dqn_model import EnhancedDQN


class OptimizedDQNAgent:
    def __init__(self, state_size, history_dim, product_dim, batch_size, action_size=1000,
                 device="cuda", gamma=0.95, use_double_dqn=True, debug_freq=50):
        self.state_size = state_size
        self.action_size = action_size
        self.device = device
        self.use_double_dqn = use_double_dqn
        self.debug_freq = debug_freq
        self.replay_count = 0

        # 改进的网络架构
        self.dqn = EnhancedDQN(history_dim, product_dim, action_size).to(self.device)
        self.target_dqn = EnhancedDQN(history_dim, product_dim, action_size).to(self.device)
        self.target_dqn.load_state_dict(self.dqn.state_dict())

        for param in self.target_dqn.parameters():
            param.requires_grad = False

        # 优化的训练参数
        self.optimizer = optim.AdamW(self.dqn.parameters(), lr=0.001, weight_decay=1e-4)
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(self.optimizer, T_max=1000, eta_min=1e-5)

        self.gamma = gamma
        # 改进的探索策略
        self.epsilon = 0.9  # 降低初始探索率
        self.epsilon_min = 0.1  # 保持适度探索
        self.epsilon_decay = 0.995
        self.batch_size = batch_size
        self.training_steps = 0

        # 添加共享embedding管理器
        from utils.data_loader import SharedEmbeddingManager
        self.emb_manager = SharedEmbeddingManager()

    def act(self, state, candidate_ids_list=None, eval_mode=False, top_k=50):
        """改进的动作选择策略"""
        history_actions, product_samples = state
        batch_size = history_actions.shape[0]
        action_size = product_samples.size(1)

        if eval_mode:
            with torch.no_grad():
                q_values = self.dqn(history_actions, product_samples)
                return torch.topk(q_values, min(top_k, action_size), dim=1).indices

        # 改进的探索策略
        if random.random() < self.epsilon:
            # 使用Boltzmann探索而不是完全随机
            with torch.no_grad():
                q_values = self.dqn(history_actions, product_samples)
                # 温度参数控制探索程度
                temperature = max(0.1, self.epsilon)
                action_probs = F.softmax(q_values / temperature, dim=1)
                return torch.multinomial(action_probs, 1).squeeze(1)
        else:
            with torch.no_grad():
                q_values = self.dqn(history_actions, product_samples)
                return torch.argmax(q_values, dim=1)

    def get_recommendations(self, state, top_k=30):
        """修复的推荐方法 - 确保索引有效性"""
        history_actions, product_samples = state
        action_size = product_samples.size(1)
        
        with torch.no_grad():
            q_values = self.dqn(history_actions, product_samples)
            
            # 确保top_k不超过实际动作空间大小
            effective_top_k = min(top_k, action_size)
            
            # 获取排序后的索引
            sorted_indices = torch.argsort(q_values, dim=1, descending=True)
            top_indices = sorted_indices[:, :effective_top_k]
            
            # 验证索引有效性
            batch_size = top_indices.size(0)
            for i in range(batch_size):
                for j in range(effective_top_k):
                    idx = top_indices[i, j].item()
                    if idx >= action_size:
                        print(f"警告: 索引 {idx} 超出范围 {action_size}")
                        top_indices[i, j] = 0  # 设为第一个有效索引
            
            return top_indices, q_values

    def train_step(self, state, action, reward, next_state, done):
        """单步训练方法"""
        history, products = state
        next_history, next_products = next_state
        
        # 计算当前Q值
        current_q_values = self.dqn(history, products)
        current_q = current_q_values.gather(1, action.unsqueeze(1)).squeeze(1)
        
        # 计算目标Q值
        with torch.no_grad():
            if self.use_double_dqn:
                next_q_current = self.dqn(next_history, next_products)
                next_actions = torch.argmax(next_q_current, dim=1)
                next_q_target = self.target_dqn(next_history, next_products)
                max_next_q = next_q_target.gather(1, next_actions.unsqueeze(1)).squeeze(1)
            else:
                next_q_target = self.target_dqn(next_history, next_products)
                max_next_q = torch.max(next_q_target, dim=1)[0]
            
            target_q = reward + (1 - done.float()) * self.gamma * max_next_q
        
        # 计算损失
        loss = F.mse_loss(current_q, target_q)
        
        # 优化步骤
        self.optimizer.zero_grad()
        loss.backward()
        torch.nn.utils.clip_grad_norm_(self.dqn.parameters(), 1.0)
        self.optimizer.step()
        self.scheduler.step()
        
        # 更新目标网络
        self.training_steps += 1
        if self.training_steps % 100 == 0:
            self.update_target_network()
        
        # 衰减探索率
        self.epsilon = max(self.epsilon_min, self.epsilon * self.epsilon_decay)
        
        return loss.item()

    def update_target_network(self):
        """软更新目标网络"""
        tau = 0.005  # 软更新参数
        for target_param, local_param in zip(self.target_dqn.parameters(), self.dqn.parameters()):
            target_param.data.copy_(tau * local_param.data + (1.0 - tau) * target_param.data)

    def save_model(self, path):
        """保存模型"""
        torch.save({
            'dqn_state_dict': self.dqn.state_dict(),
            'target_dqn_state_dict': self.target_dqn.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'epsilon': self.epsilon,
            'training_steps': self.training_steps
        }, path)

    def load_model(self, path):
        """加载模型"""
        checkpoint = torch.load(path, map_location=self.device)
        self.dqn.load_state_dict(checkpoint['dqn_state_dict'])
        self.target_dqn.load_state_dict(checkpoint['target_dqn_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        self.epsilon = checkpoint['epsilon']
        self.training_steps = checkpoint['training_steps']


def create_optimized_training_loop():
    """创建优化的训练循环"""
    
    def train_optimized_dqn(agent, dataloader, env, epochs=10, device="cuda"):
        """优化的DQN训练循环"""
        agent.dqn.train()
        
        for epoch in range(epochs):
            total_loss = 0.0
            total_reward = 0.0
            num_batches = 0
            
            print(f"\n=== Epoch {epoch + 1}/{epochs} ===")
            
            for batch_idx, batch in enumerate(dataloader):
                history_actions = batch[0].to(device, dtype=torch.float32)
                product_samples = batch[1].to(device, dtype=torch.float32)
                target_item_ids = batch[2].to(device, dtype=torch.float32)
                candidate_ids_list = batch[4]
                
                state = (history_actions, product_samples)
                
                # 智能体选择动作
                actions = agent.act(state, candidate_ids_list=candidate_ids_list, eval_mode=False)
                
                # 计算归一化商品ID
                batch_size = actions.size(0)
                norm_item_ids = torch.zeros(batch_size, device=device, dtype=torch.float32)
                
                for i in range(batch_size):
                    action_idx = actions[i].item()
                    if action_idx < len(candidate_ids_list[i]):
                        chosen_item_id = candidate_ids_list[i][action_idx]
                        norm_item_ids[i] = chosen_item_id / float(env.max_item_id)
                    else:
                        # 如果索引无效，选择第一个商品
                        chosen_item_id = candidate_ids_list[i][0]
                        norm_item_ids[i] = chosen_item_id / float(env.max_item_id)
                
                # 环境执行动作
                next_state, rewards, dones, _ = env.step(state, norm_item_ids, candidate_ids_list)
                
                # 训练智能体
                loss = agent.train_step(state, actions, rewards, next_state, dones)
                
                total_loss += loss
                total_reward += rewards.mean().item()
                num_batches += 1
                
                if batch_idx % 10 == 0:
                    print(f"  Batch {batch_idx}: Loss={loss:.4f}, Reward={rewards.mean().item():.4f}, ε={agent.epsilon:.3f}")
            
            avg_loss = total_loss / num_batches
            avg_reward = total_reward / num_batches
            
            print(f"Epoch {epoch + 1} 完成: 平均损失={avg_loss:.4f}, 平均奖励={avg_reward:.4f}")
        
        return agent
    
    return train_optimized_dqn
