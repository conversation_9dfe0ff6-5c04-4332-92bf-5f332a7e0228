# data_loader.py
import torch
import random
import numpy as np
from torch.utils.data import Dataset
from tqdm import tqdm
# import faiss  # 移除faiss依赖
import os
import threading

current_dir = os.path.dirname(os.path.abspath(__file__))
data_dir = os.path.join(current_dir, '..', 'data')
device = "cuda" if torch.cuda.is_available() else "cpu"


class SharedEmbeddingManager:
    """共享embedding管理器，避免重复加载"""
    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if self._initialized:
            return

        print("初始化共享embedding管理器...")
        # 使用mmap加载embedding矩阵
        self.item_emb_matrix = np.load(os.path.join(data_dir, 'item_emb_matrix.npy'), mmap_mode='r')
        self.item_id_map = np.load(os.path.join(data_dir, 'item_id_map.npy'))
        self.idx2itemid = {idx: item_id for idx, item_id in enumerate(self.item_id_map)}
        self.itemid2idx = {item_id: idx for idx, item_id in enumerate(self.item_id_map)}
        self.valid_product_ids = set(self.item_id_map.tolist())
        self.max_item_id = int(max(self.valid_product_ids)) if self.valid_product_ids else 1

        # 构建numpy索引替代FAISS
        self._build_numpy_index()

        # 加载商品流行度
        self._load_popularity()

        self._initialized = True
        print(f"共享embedding管理器初始化完成，包含 {len(self.valid_product_ids)} 个商品")

    def _build_numpy_index(self):
        print("构建numpy索引...")
        # 分批加载embedding避免内存峰值
        batch_size = 1000
        embeddings_list = []

        for i in range(0, len(self.item_emb_matrix), batch_size):
            end_idx = min(i + batch_size, len(self.item_emb_matrix))
            batch_emb = np.array(self.item_emb_matrix[i:end_idx], dtype=np.float32)
            embeddings_list.append(batch_emb)

        self.embeddings = np.vstack(embeddings_list)
        # 归一化
        norms = np.linalg.norm(self.embeddings, axis=1, keepdims=True)
        self.embeddings_normalized = self.embeddings / (norms + 1e-8)

        # 释放临时变量
        del embeddings_list
        print(f"numpy索引构建完成，包含 {len(self.item_emb_matrix)} 个商品")

    def _load_popularity(self):
        popularity_path = os.path.join(data_dir, 'item_popularity.npy')
        if os.path.exists(popularity_path):
            try:
                self.item_popularity = np.load(popularity_path, allow_pickle=True).item()
                print(f"已加载预计算的商品流行度，包含 {len(self.item_popularity)} 个商品")
            except Exception as e:
                print(f"加载商品流行度失败: {str(e)}")
                self.item_popularity = {}
        else:
            self.item_popularity = {}

        self.popular_items = sorted(self.item_popularity.items(),
                                    key=lambda x: x[1], reverse=True)
        self.popular_items = [x[0] for x in self.popular_items] if self.popular_items else list(self.valid_product_ids)

    def get_embedding(self, item_id):
        """获取单个商品的embedding，避免大量内存拷贝"""
        if item_id not in self.itemid2idx:
            return torch.zeros(self.item_emb_matrix.shape[1], dtype=torch.float32)

        idx = self.itemid2idx[item_id]
        # 直接从mmap中读取，避免拷贝
        embedding = self.item_emb_matrix[idx]
        return torch.from_numpy(embedding.copy()).float()  # 只拷贝单个embedding

    def get_similar_items(self, item_id, top_n=300):
        if item_id not in self.itemid2idx:
            return []

        idx = self.itemid2idx[item_id]
        embedding = self.embeddings_normalized[idx:idx+1]  # 保持2D形状
        
        # 用numpy计算余弦相似度
        similarities = np.dot(self.embeddings_normalized, embedding.T).flatten()
        
        # 获取top_n个最相似的item（排除自己）
        top_indices = np.argsort(similarities)[::-1][1:top_n+1]
        
        similar_items = []
        for idx in top_indices:
            if idx < len(self.idx2itemid):
                similar_items.append(self.idx2itemid[idx])
        
        return similar_items


class RecommendationDataset(Dataset):
    def __init__(self, user_history_actions, product_sample_num=1000,
                 popularity_path=None, do_train=True):
        self.user_history_actions = user_history_actions
        self.product_sample_num = product_sample_num
        self.do_train = do_train
        self.max_length = 10

        # 使用共享embedding管理器
        self.emb_manager = SharedEmbeddingManager()

        self.product_feature_dim = self.emb_manager.item_emb_matrix.shape[1]  # 768
        self.behavior_feature_dim = 5  # 保留但不使用
        self.d_model = self.product_feature_dim  # 改为768，不再添加行为特征

        self.label_map = {
            'none': 0, 'click': 1, 'alipay': 2, 'cart': 3,
            'collect': 4,
        }

        # 预计算一些常用的tensor以避免重复创建
        self.zero_product_feature = torch.zeros(self.product_feature_dim, dtype=torch.float32)
        self.zero_combined_feature = torch.zeros(self.d_model, dtype=torch.float32)


    def get_valid_item_id(self, item_id):
        return item_id if item_id in self.emb_manager.valid_product_ids else 0

    def one_hot_encoding(self, category):
        one_hot = torch.zeros(self.behavior_feature_dim, dtype=torch.float32)
        behavior_map = {
            'none': 0, 'click': 1, 'alipay': 2, 'cart': 3,
            'collect': 4,
        }
        if category in behavior_map:
            index = min(behavior_map[category], self.behavior_feature_dim - 1)
            one_hot[index] = 1
        return one_hot

    def __len__(self):
        return len(self.user_history_actions)

    def __getitem__(self, idx):
        try:
            sample = self.user_history_actions[idx]
            history_actions = sample["history_actions"]
            target_item_id = sample["item_id"]
            action = sample["action"]

            # 确保目标商品ID是整数
            if hasattr(target_item_id, 'item'):
                target_item_id = target_item_id.item()
            else:
                target_item_id = int(target_item_id)

            # 构建候选商品池（优化版本）
            candidate_pool = self._build_candidate_pool(history_actions, target_item_id)

            # 构建历史行为序列（优化版本）
            history_actions_tensor = self._build_history_tensor(history_actions)

            # 构建商品特征张量（优化版本）
            product_sample_tensor = self._build_product_tensor(candidate_pool)

            # 验证目标商品在候选池中（防御性编程）
            if target_item_id not in candidate_pool:
                print(f"严重错误: 目标商品 {target_item_id} 不在候选池中，强制添加到第一个位置")
                candidate_pool[0] = target_item_id
                # 重新构建product tensor
                product_sample_tensor = self._build_product_tensor(candidate_pool)

            # 确保目标商品在第一个位置（便于调试）
            if candidate_pool[0] != target_item_id:
                # 找到目标商品的位置并交换到第一个位置
                try:
                    target_idx = candidate_pool.index(target_item_id)
                    candidate_pool[0], candidate_pool[target_idx] = candidate_pool[target_idx], candidate_pool[0]
                    # 重新构建product tensor
                    product_sample_tensor = self._build_product_tensor(candidate_pool)
                except ValueError:
                    print(f"错误: 目标商品 {target_item_id} 不在候选池中")
                    candidate_pool[0] = target_item_id
                    product_sample_tensor = self._build_product_tensor(candidate_pool)

            recommend_item_id_norm = torch.tensor([target_item_id / float(self.emb_manager.max_item_id)],
                                                  dtype=torch.float32)
            label = torch.tensor(self.label_map.get(action, 0), dtype=torch.long)

            return (history_actions_tensor, product_sample_tensor, recommend_item_id_norm, label, candidate_pool)

        except Exception as e:
            print(f'[Dataset __getitem__] index={idx} 出错: {str(e)}')
            # 返回默认值避免崩溃
            return self._get_default_item()

    def _build_candidate_pool(self, history_actions, target_item_id):
        """优化的候选商品池构建"""
        candidate_pool = set()
        total_needed = self.product_sample_num

        # 调试信息（可关闭以加速训练）
        # print(f"构建候选池: target_item_id={target_item_id}, total_needed={total_needed}")

        # 添加目标商品 - 强制添加到第一个位置，即使不在有效商品列表中
        candidate_list = [target_item_id]  # 确保目标商品在第一个位置
        candidate_pool.add(target_item_id)

        # 添加相似商品（基于最近3个历史商品）
        recent_items = [action[0] for action in history_actions[-3:]]
        for item_id in recent_items:
            if len(candidate_pool) >= total_needed:
                break
            # 确保item_id是整数
            if hasattr(item_id, 'item'):
                item_id = item_id.item()
            else:
                item_id = int(item_id)

            top_n = min(100, total_needed - len(candidate_pool))
            similar_items = self.emb_manager.get_similar_items(item_id, top_n=top_n)
            for sim_item in similar_items:
                if sim_item not in candidate_pool and len(candidate_pool) < total_needed:
                    candidate_pool.add(sim_item)
                    candidate_list.append(sim_item)

        # 添加热门商品
        if len(candidate_pool) < total_needed:
            for item in self.emb_manager.popular_items:
                if item in candidate_pool:
                    continue
                candidate_pool.add(item)
                candidate_list.append(item)
                if len(candidate_pool) >= total_needed:
                    break

        # 随机填充
        if len(candidate_pool) < total_needed:
            available_ids = [id for id in self.emb_manager.valid_product_ids if id not in candidate_pool]
            if available_ids:
                needed = total_needed - len(candidate_pool)
                random_sample = random.sample(available_ids, min(needed, len(available_ids)))
                for item in random_sample:
                    candidate_pool.add(item)
                    candidate_list.append(item)

        # 确保大小正确
        candidate_list = candidate_list[:total_needed]
        while len(candidate_list) < total_needed:
            candidate_list.append(0)

        # 验证目标商品确实在候选池中
        if target_item_id not in candidate_list:
            print(f"严重错误: 目标商品 {target_item_id} 不在候选池中!")
            candidate_list[0] = target_item_id  # 强制放在第一个位置

        # 调试信息（可关闭以加速训练）
        # print(f"候选池构建完成: 大小={len(candidate_list)}, 目标商品在第{candidate_list.index(target_item_id) if target_item_id in candidate_list else -1}位")

        return candidate_list

    def _build_history_tensor(self, history_actions):
        """优化的历史序列构建，现在只使用商品嵌入"""
        history_actions_tensor = torch.zeros((self.max_length, self.d_model), dtype=torch.float32)
        seq_len = min(len(history_actions), self.max_length)

        for i in range(seq_len):
            item_id, action_type = history_actions[i]
            # 确保item_id是整数
            if hasattr(item_id, 'item'):
                item_id = item_id.item()
            else:
                item_id = int(item_id)

            valid_item_id = self.get_valid_item_id(item_id)

            if valid_item_id == 0:
                continue

            # 只使用商品嵌入，不再添加行为特征
            product_feature = self.emb_manager.get_embedding(valid_item_id)
            combined_feature = product_feature  # 直接使用商品嵌入

            # 确保维度一致
            if len(combined_feature) < self.d_model:
                padding = torch.zeros(self.d_model - len(combined_feature), dtype=torch.float32)
                combined_feature = torch.cat([combined_feature, padding])
            elif len(combined_feature) > self.d_model:
                combined_feature = combined_feature[:self.d_model]

            history_actions_tensor[i] = combined_feature

        return history_actions_tensor

    def _build_product_tensor(self, candidate_pool):
        """优化的商品特征张量构建"""
        product_features = []

        for item_id in candidate_pool:
            if item_id == 0:
                product_features.append(self.zero_product_feature.clone())
            else:
                feature = self.emb_manager.get_embedding(item_id)
                product_features.append(feature)

        # 确保大小正确
        while len(product_features) < self.product_sample_num:
            product_features.append(self.zero_product_feature.clone())

        if len(product_features) > self.product_sample_num:
            product_features = product_features[:self.product_sample_num]

        return torch.stack(product_features)

    def _get_default_item(self):
        """返回默认项，避免错误时崩溃"""
        history_actions_tensor = torch.zeros((self.max_length, self.d_model), dtype=torch.float32)
        product_sample_tensor = torch.zeros((self.product_sample_num, self.product_feature_dim), dtype=torch.float32)
        recommend_item_id_norm = torch.tensor([0.0], dtype=torch.float32)
        label = torch.tensor(0, dtype=torch.long)
        candidate_pool = [0] * self.product_sample_num

        return (history_actions_tensor, product_sample_tensor, recommend_item_id_norm, label, candidate_pool)