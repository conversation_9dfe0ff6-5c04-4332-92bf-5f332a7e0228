import torch
import numpy as np
import os
import gc
import copy
from sklearn.model_selection import KFold
from torch.utils.data import DataLoader, Subset
from models.reward_model import RewardModel
from models.recommendation_env import RecommendationEnv
from dqn_agent import DQNAgent
from utils.data_loader import RecommendationDataset
from utils.metrics import calculate_metrics
from tqdm import tqdm

current_dir = os.path.dirname(os.path.abspath(__file__))
data_dir = os.path.join(current_dir, 'data')
output_dir = os.path.join(current_dir, 'output')
os.makedirs(output_dir, exist_ok=True)

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")

HYPERPARAMS = {
    'gamma': 0.99,
    'beta': 0.05,
    'alpha': 0.1,
    'eta': 0.05,
    'Tp': 60,
    'k_folds': 5,
    'top_ks': [5, 10, 20, 30, 40, 50]
}


class GPUMemoryManager:
    def __init__(self, max_alloc=0.8):
        self.max_alloc = max_alloc
        self.total_mem = torch.cuda.get_device_properties(0).total_memory if torch.cuda.is_available() else 0

    def should_clear(self):
        if torch.cuda.is_available():
            allocated = torch.cuda.memory_allocated(0)
            return allocated > self.total_mem * self.max_alloc
        return False

    def clear_if_needed(self):
        if self.should_clear():
            torch.cuda.empty_cache()


def evaluate(agent, dataloader, top_ks, device, max_item_id):
    agent.dqn.eval()
    all_metrics = {k: {'precision': [], 'recall': [], 'f1': [], 'ndcg': []} for k in top_ks}

    memory_manager = GPUMemoryManager()

    with torch.no_grad():
        for batch_idx, batch in enumerate(tqdm(dataloader, desc="Evaluating")):
            # 将数据移动到设备
            history_actions = batch[0].to(device, dtype=torch.float32)
            product_samples = batch[1].to(device, dtype=torch.float32)
            target_item_ids = batch[2].cpu().numpy()
            labels = batch[3].cpu().numpy()
            sampled_item_ids_list = batch[4]  # 候选商品池

            # 修复：确保候选商品ID是整数类型
            converted_sampled_item_ids_list = []
            for item_ids in sampled_item_ids_list:
                int_item_ids = []
                for item_id in item_ids:
                    if hasattr(item_id, 'item'):
                        int_item_ids.append(item_id.item())
                    elif torch.is_tensor(item_id):
                        int_item_ids.append(item_id.item())
                    else:
                        int_item_ids.append(int(item_id))
                converted_sampled_item_ids_list.append(int_item_ids)
            sampled_item_ids_list = converted_sampled_item_ids_list

            state = (history_actions, product_samples)
            # 获取top_k推荐，k取最大值
            recommendations, _ = agent.get_recommendations(state, top_k=max(top_ks))

            for i in range(len(recommendations)):
                rec_indices = recommendations[i].cpu().numpy()
                # 修复：target_item_ids已经是归一化的，需要还原为原始ID
                true_item_id = int(target_item_ids[i] * max_item_id)
                true_items = [true_item_id]

                # 获取推荐的商品ID - 修复处理方式
                rec_item_ids = []
                candidate_pool_size = len(sampled_item_ids_list[i])
                for idx in rec_indices:
                    if idx < candidate_pool_size:
                        item_id = sampled_item_ids_list[i][idx]
                        # 确保是整数类型
                        if hasattr(item_id, 'item'):
                            item_id = item_id.item()
                        elif torch.is_tensor(item_id):
                            item_id = item_id.item()
                        rec_item_ids.append(int(item_id))
                    else:
                        # 如果索引超出范围，使用候选池中的随机商品
                        fallback_idx = idx % candidate_pool_size
                        item_id = sampled_item_ids_list[i][fallback_idx]
                        if hasattr(item_id, 'item'):
                            item_id = item_id.item()
                        elif torch.is_tensor(item_id):
                            item_id = item_id.item()
                        rec_item_ids.append(int(item_id))

                # 调试信息 - 前几个批次打印详细信息
                debug_info = {'debug': batch_idx < 2 and i < 3}  # 前2个批次的前3个样本开启调试

                # 计算不同top_k的指标
                for k in top_ks:
                    precision, recall, f1, ndcg = calculate_metrics(
                        rec_item_ids, true_items, k, debug_info
                    )

                    all_metrics[k]['precision'].append(precision)
                    all_metrics[k]['recall'].append(recall)
                    all_metrics[k]['f1'].append(f1)
                    all_metrics[k]['ndcg'].append(ndcg)

                    # 打印调试信息
                    if debug_info['debug'] and k == top_ks[0]:  # 只在第一个k值时打印
                        print(f"\n=== 调试信息 (batch {batch_idx}, sample {i}) ===")
                        print(f"目标商品ID: {true_item_id}")
                        print(f"候选池大小: {len(sampled_item_ids_list[i])}")
                        print(f"目标商品是否在候选池中: {true_item_id in sampled_item_ids_list[i]}")
                        print(f"推荐商品ID (top {k}): {rec_item_ids[:k]}")
                        print(f"目标商品是否在推荐中: {true_item_id in rec_item_ids[:k]}")
                        print(f"指标 - precision: {precision:.4f}, recall: {recall:.4f}, f1: {f1:.4f}")

                        # 检查候选池中的商品ID范围
                        if sampled_item_ids_list[i]:
                            pool_min = min(sampled_item_ids_list[i])
                            pool_max = max(sampled_item_ids_list[i])
                            print(f"候选池商品ID范围: {pool_min} - {pool_max}")

                        # 检查推荐结果中的无效ID
                        invalid_recs = [id for id in rec_item_ids[:k] if id == -1]
                        if invalid_recs:
                            print(f"警告: 推荐结果中有 {len(invalid_recs)} 个无效商品ID (-1)")
                        print("=" * 50)

            memory_manager.clear_if_needed()
            del history_actions, product_samples
            gc.collect()

    avg_metrics = {}
    for k in top_ks:
        avg_metrics[k] = {
            'precision': np.mean(all_metrics[k]['precision']),
            'recall': np.mean(all_metrics[k]['recall']),
            'f1': np.mean(all_metrics[k]['f1']),
            'ndcg': np.mean(all_metrics[k]['ndcg'])
        }

    return avg_metrics


def main():
    config = {
        'learning_rate': 0.001,
        'batch_size': 64,  # 可调整：32(快速) 或 64(标准)
        'epochs': 5,       # 可调整：2(快速) 或 5(标准) 或 10(最佳)
        'history_dim': 768,  # 773维 (768+5)
        'product_dim': 768,
        'product_sample_num': 1000,  # 可调整：500(快速) 或 1000(标准)
        'feedback_num': 5,  # 5种反馈类型
        'reward_model_path': os.path.join(output_dir, 'reward_model_weights.pth'),
        'model_path': os.path.join(output_dir, 'dqn_model_weights.pth'),
        'k_folds': 5,      # 可调整：2(快速) 或 5(标准)
        'top_ks': [5, 10, 20, 50]  # 评估指标
    }

    print("加载处理后的数据...")
    user_history_actions = np.load(os.path.join(data_dir, "user_history_actions.npy"), allow_pickle=True)
    # 可以调整数据量：使用全部数据获得最佳效果，或减少数据量加快训练
    # user_history_actions = user_history_actions[:1000]  # 快速测试用1000个样本
    # user_history_actions = user_history_actions[:5000]  # 中等规模用5000个样本
    # 获取最大商品ID（从共享管理器）
    from utils.data_loader import SharedEmbeddingManager
    emb_manager = SharedEmbeddingManager()
    max_item_id = emb_manager.max_item_id

    print(f"加载数据完成! 总样本数: {len(user_history_actions)}, 最大商品ID: {max_item_id}")

    kf = KFold(n_splits=config['k_folds'], shuffle=True)
    fold_results = []
    best_f1 = 0.0
    best_model = None

    for fold, (train_idx, test_idx) in enumerate(kf.split(user_history_actions)):
        print(f"\n{'=' * 50}")
        print(f"Fold {fold + 1}/{config['k_folds']}")
        print(f"{'=' * 50}")

        train_data = [user_history_actions[i] for i in train_idx]
        test_data = [user_history_actions[i] for i in test_idx]

        print(f"训练样本数: {len(train_data)}, 测试样本数: {len(test_data)}")

        # 不传递encoded_products参数
        train_dataset = RecommendationDataset(train_data, product_sample_num=config['product_sample_num'])
        test_dataset = RecommendationDataset(test_data, product_sample_num=config['product_sample_num'], do_train=False)

        # 设置num_workers=0避免多进程内存问题
        def custom_collate_fn(batch):
            """自定义批处理函数，保持候选池列表不被压缩"""
            history_tensors = torch.stack([item[0] for item in batch])
            product_tensors = torch.stack([item[1] for item in batch])
            target_norms = torch.stack([item[2] for item in batch])
            labels = torch.stack([item[3] for item in batch])
            candidate_pools = [item[4] for item in batch]  # 保持为列表，不进行stack
            return history_tensors, product_tensors, target_norms, labels, candidate_pools

        train_dataloader = DataLoader(
            train_dataset,
            batch_size=config['batch_size'],
            shuffle=True,
            num_workers=0,
            pin_memory=False,
            collate_fn=custom_collate_fn
        )

        test_dataloader = DataLoader(
            test_dataset,
            batch_size=config['batch_size'],
            shuffle=False,
            num_workers=0,
            pin_memory=False,
            collate_fn=custom_collate_fn
        )

        print(f"初始化奖励模型...")
        reward_model = RewardModel(
            history_feature_dim=config['history_dim'],
            product_feature_dim=config['product_dim'],
            output_dim=config['feedback_num']
        ).to(device)

        # 加载预训练的奖励模型权重
        if os.path.exists(config['reward_model_path']):
            print(f"加载奖励模型权重: {config['reward_model_path']}")
            reward_model.load_state_dict(torch.load(config['reward_model_path'], map_location=device))
        else:
            print(f"警告: 未找到奖励模型权重文件 {config['reward_model_path']}")

        reward_model.eval()

        print(f"创建推荐环境...")
        env = RecommendationEnv(
            config['product_sample_num'],
            reward_model,
            max_item_id,  # 传入最大商品ID
            device=device,
            history_dim=config['history_dim']
        )

        # 初始化DQN智能体，动作空间设为1000
        print(f"初始化DQN智能体...")
        agent = DQNAgent(
            config['product_sample_num'],  # 1000
            config['history_dim'],
            config['product_dim'],
            config['batch_size'],
            action_size=config['product_sample_num'],  # 1000
            device=device,
            gamma=HYPERPARAMS['gamma']
        )

        memory_manager = GPUMemoryManager()

        # 在训练循环部分进行修改
        for epoch in range(config['epochs']):
            agent.dqn.train()
            total_reward = 0
            num_batches = 0
            epoch_loss = 0.0

            progress_bar = tqdm(train_dataloader, desc=f'Epoch {epoch + 1}/{config["epochs"]}')

            # 每50步打印一次
            print_freq = 50

            # 在训练循环中，获取candidate_ids
            for batch_idx, batch in enumerate(progress_bar):
                # 将数据移动到设备
                history_actions = batch[0].to(device, dtype=torch.float32)
                product_samples = batch[1].to(device, dtype=torch.float32)
                sampled_item_ids_list = batch[4]  # 候选商品池

                # 修复：确保候选商品ID是整数类型
                converted_sampled_item_ids_list = []
                for item_ids in sampled_item_ids_list:
                    int_item_ids = []
                    for item_id in item_ids:
                        if hasattr(item_id, 'item'):
                            int_item_ids.append(item_id.item())
                        elif torch.is_tensor(item_id):
                            int_item_ids.append(item_id.item())
                        else:
                            int_item_ids.append(int(item_id))
                    converted_sampled_item_ids_list.append(int_item_ids)
                sampled_item_ids_list = converted_sampled_item_ids_list

                state = (history_actions, product_samples)
                candidate_ids_list = sampled_item_ids_list

                # 智能体选择动作
                actions = agent.act(state, candidate_ids_list=candidate_ids_list, eval_mode=False)

                # 获取归一化的商品ID
                batch_size = actions.size(0)
                norm_item_ids = torch.zeros(batch_size, device=device, dtype=torch.float32)

                for i in range(batch_size):
                    action_idx = actions[i].item()
                    if action_idx < len(candidate_ids_list[i]):
                        chosen_item_id = candidate_ids_list[i][action_idx]
                        norm_item_ids[i] = chosen_item_id / float(max_item_id)
                    else:
                        chosen_item_id = candidate_ids_list[i][0]
                        norm_item_ids[i] = chosen_item_id / float(max_item_id)

                # 环境执行动作并返回奖励 - 修改：接收next_candidate_ids_list
                next_state, rewards, dones, next_candidate_ids_list = env.step(state, norm_item_ids,
                                                                               candidate_ids_list)

                # 存储经验并训练 - 修改：传递candidate_ids
                agent.remember(state, actions, rewards, next_state, dones, candidate_ids_list,
                               next_candidate_ids_list)
                loss = agent.replay()

                if loss is not None:
                    epoch_loss += loss

                total_reward += rewards.mean().item()
                num_batches += 1

                if batch_idx % print_freq == 0 or batch_idx == len(train_dataloader) - 1:
                    progress_bar.set_postfix({
                        'avg_reward': f'{total_reward / num_batches:.3f}',
                        'loss': f'{loss if loss is not None else 0:.4f}',
                        'epsilon': f'{agent.epsilon:.2f}'
                    })

                # 清理内存
                memory_manager.clear_if_needed()
                del history_actions, product_samples, next_state, rewards, dones
                gc.collect()

            # 评估模型
            print(f"\nFold {fold + 1}, Epoch {epoch + 1} 评估中...")
            eval_metrics = evaluate(agent, test_dataloader, config['top_ks'], device, max_item_id)

            print(f"\nFold {fold + 1}, Epoch {epoch + 1} 评估结果:")
            for k in config['top_ks']:
                metrics = eval_metrics[k]
                print(f"指标 @ {k}:")
                print(f"  精确率: {metrics['precision']:.4f}")
                print(f"  召回率: {metrics['recall']:.4f}")
                print(f"  F1值: {metrics['f1']:.4f}")
                print(f"  NDCG: {metrics['ndcg']:.4f}")

            # 获取当前F1分数
            current_f1 = eval_metrics[max(config['top_ks'])]['f1']

            # 保存最佳模型
            if current_f1 > best_f1:
                best_f1 = current_f1
                best_model = copy.deepcopy(agent.dqn.state_dict())
                torch.save(best_model, config['model_path'])
                print(f"保存新最佳模型! F1@{max(config['top_ks'])}: {best_f1:.4f}")
                print(f"当前学习率: {agent.optimizer.param_groups[0]['lr']:.6f}")

            # 清理内存
            memory_manager.clear_if_needed()
            gc.collect()

        # 保存当前fold的最终模型
        fold_model_path = f"{config['model_path']}.fold{fold + 1}"
        torch.save(agent.dqn.state_dict(), fold_model_path)
        fold_results.append(eval_metrics)

    # 打印交叉验证结果
    print("\n最终交叉验证结果:")
    for k in config['top_ks']:
        avg_precision = np.mean([result[k]['precision'] for result in fold_results])
        avg_recall = np.mean([result[k]['recall'] for result in fold_results])
        avg_f1 = np.mean([result[k]['f1'] for result in fold_results])
        avg_ndcg = np.mean([result[k]['ndcg'] for result in fold_results])

        print(f"\n平均指标 @ {k}:")
        print(f"  精确率: {avg_precision:.4f}")
        print(f"  召回率: {avg_recall:.4f}")
        print(f"  F1值: {avg_f1:.4f}")
        print(f"  NDCG: {avg_ndcg:.4f}")

    # 保存最终模型
    final_model_path = f"{config['model_path']}.final"
    torch.save(best_model, final_model_path)
    print(f"\n训练完成! 最佳F1值: {best_f1:.4f}")
    print(f"最终模型已保存至: {final_model_path}")


if __name__ == "__main__":
    main()
