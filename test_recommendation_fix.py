#!/usr/bin/env python3
"""
最小化测试：验证推荐系统的候选池构建和评估逻辑
"""

import torch
import numpy as np
import os
from torch.utils.data import DataLoader
from models.reward_model import RewardModel
from models.recommendation_env import RecommendationEnv
from dqn_agent import DQNAgent
from utils.data_loader import RecommendationDataset
from utils.metrics import calculate_metrics

# 设置设备
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"使用设备: {device}")

def test_candidate_pool_construction():
    """测试候选池构建逻辑"""
    print("\n=== 测试1: 候选池构建 ===")
    
    # 加载少量数据进行测试
    user_history_actions = np.load("./data/user_history_actions.npy", allow_pickle=True)
    test_data = user_history_actions[:10]  # 只取前10个样本
    
    # 创建数据集
    dataset = RecommendationDataset(test_data, product_sample_num=1000)
    print(f"数据集创建完成，样本数: {len(dataset)}")
    
    # 测试前3个样本
    for i in range(3):
        print(f"\n--- 样本 {i} ---")
        try:
            sample = dataset[i]
            history_tensor, product_tensor, target_norm, label, candidate_pool = sample
            
            print(f"历史特征形状: {history_tensor.shape}")
            print(f"商品特征形状: {product_tensor.shape}")
            print(f"候选池大小: {len(candidate_pool)}")
            print(f"目标商品归一化ID: {target_norm.item():.4f}")
            
            # 计算真实目标商品ID
            max_item_id = dataset.emb_manager.max_item_id
            true_item_id = int(target_norm.item() * max_item_id)
            print(f"目标商品ID: {true_item_id}")
            print(f"目标商品是否在候选池中: {true_item_id in candidate_pool}")
            
            if true_item_id in candidate_pool:
                target_position = candidate_pool.index(true_item_id)
                print(f"目标商品在候选池第 {target_position} 位")
            
            # 检查候选池中的有效商品数量
            valid_items = [item for item in candidate_pool if item != 0]
            print(f"候选池中有效商品数量: {len(valid_items)}")
            
        except Exception as e:
            print(f"样本 {i} 处理失败: {e}")
    
    return dataset

def test_dqn_recommendation(dataset):
    """测试DQN推荐逻辑"""
    print("\n=== 测试2: DQN推荐 ===")
    
    # 自定义批处理函数
    def custom_collate_fn(batch):
        """自定义批处理函数，保持候选池列表不被压缩"""
        history_tensors = torch.stack([item[0] for item in batch])
        product_tensors = torch.stack([item[1] for item in batch])
        target_norms = torch.stack([item[2] for item in batch])
        labels = torch.stack([item[3] for item in batch])
        candidate_pools = [item[4] for item in batch]  # 保持为列表，不进行stack
        return history_tensors, product_tensors, target_norms, labels, candidate_pools

    # 创建数据加载器
    dataloader = DataLoader(dataset, batch_size=4, shuffle=False, num_workers=0, collate_fn=custom_collate_fn)
    
    # 初始化DQN智能体
    config = {
        'product_sample_num': 1000,
        'history_dim': 768,
        'product_dim': 768,
        'batch_size': 4
    }
    
    agent = DQNAgent(
        config['product_sample_num'],
        config['history_dim'],
        config['product_dim'],
        config['batch_size'],
        action_size=config['product_sample_num'],
        device=device,
        gamma=0.99
    )
    
    # 测试一个批次
    batch = next(iter(dataloader))
    history_actions = batch[0].to(device, dtype=torch.float32)
    product_samples = batch[1].to(device, dtype=torch.float32)
    target_item_ids = batch[2].cpu().numpy()
    sampled_item_ids_list = batch[4]
    
    print(f"批次大小: {len(history_actions)}")
    print(f"历史特征形状: {history_actions.shape}")
    print(f"商品特征形状: {product_samples.shape}")
    
    # 获取推荐
    state = (history_actions, product_samples)
    recommendations, q_values = agent.get_recommendations(state, top_k=10)
    
    max_item_id = dataset.emb_manager.max_item_id
    
    for i in range(len(recommendations)):
        print(f"\n--- 批次样本 {i} ---")
        rec_indices = recommendations[i].cpu().numpy()
        true_item_id = int(target_item_ids[i] * max_item_id)
        candidate_pool = sampled_item_ids_list[i]
        
        print(f"目标商品ID: {true_item_id}")
        print(f"候选池大小: {len(candidate_pool)}")
        print(f"目标商品在候选池中: {true_item_id in candidate_pool}")
        
        # 获取推荐的商品ID
        rec_item_ids = []
        for idx in rec_indices[:5]:  # 只看前5个推荐
            if idx < len(candidate_pool):
                item_id = candidate_pool[idx]
                if hasattr(item_id, 'item'):
                    item_id = item_id.item()
                elif torch.is_tensor(item_id):
                    item_id = item_id.item()
                rec_item_ids.append(int(item_id))
            else:
                print(f"警告: 推荐索引 {idx} 超出候选池大小 {len(candidate_pool)}")
                # 使用fallback逻辑
                fallback_idx = idx % len(candidate_pool)
                item_id = candidate_pool[fallback_idx]
                if hasattr(item_id, 'item'):
                    item_id = item_id.item()
                elif torch.is_tensor(item_id):
                    item_id = item_id.item()
                rec_item_ids.append(int(item_id))
        
        print(f"推荐商品ID (top 5): {rec_item_ids}")
        print(f"目标商品在推荐中: {true_item_id in rec_item_ids}")
        
        # 计算指标
        true_items = [true_item_id]
        precision, recall, f1, ndcg = calculate_metrics(rec_item_ids, true_items, 5)
        print(f"指标 - precision: {precision:.4f}, recall: {recall:.4f}, f1: {f1:.4f}, ndcg: {ndcg:.4f}")

def test_metrics_calculation():
    """测试指标计算"""
    print("\n=== 测试3: 指标计算 ===")
    
    # 模拟一些测试案例
    test_cases = [
        {
            "name": "完美匹配",
            "pred_items": [123, 456, 789],
            "true_items": [123],
            "k": 3
        },
        {
            "name": "部分匹配",
            "pred_items": [111, 123, 333],
            "true_items": [123],
            "k": 3
        },
        {
            "name": "无匹配",
            "pred_items": [111, 222, 333],
            "true_items": [123],
            "k": 3
        },
        {
            "name": "多目标匹配",
            "pred_items": [123, 456, 789],
            "true_items": [123, 456],
            "k": 3
        }
    ]
    
    for case in test_cases:
        print(f"\n{case['name']}:")
        print(f"  预测: {case['pred_items']}")
        print(f"  真实: {case['true_items']}")
        
        precision, recall, f1, ndcg = calculate_metrics(
            case['pred_items'], case['true_items'], case['k']
        )
        
        print(f"  precision: {precision:.4f}")
        print(f"  recall: {recall:.4f}")
        print(f"  f1: {f1:.4f}")
        print(f"  ndcg: {ndcg:.4f}")

def main():
    """主测试函数"""
    print("开始最小化测试...")
    
    try:
        # 测试1: 候选池构建
        dataset = test_candidate_pool_construction()
        
        # 测试2: DQN推荐
        test_dqn_recommendation(dataset)
        
        # 测试3: 指标计算
        test_metrics_calculation()
        
        print("\n=== 测试完成 ===")
        print("如果看到合理的候选池大小(1000)、目标商品在候选池中、以及非零的指标值，")
        print("说明修复成功！")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
