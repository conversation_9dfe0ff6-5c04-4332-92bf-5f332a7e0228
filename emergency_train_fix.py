#!/usr/bin/env python3
"""
紧急修复训练配置 - 针对完整数据集优化
"""

import torch
import numpy as np
import os
from torch.utils.data import DataLoader
from models.reward_model import RewardModel
from models.recommendation_env import RecommendationEnv
from dqn_agent import DQNAgent
from utils.data_loader import RecommendationDataset
from utils.metrics import calculate_metrics
from tqdm import tqdm

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

def create_emergency_config():
    """创建紧急修复的配置"""
    return {
        'history_dim': 768,
        'product_dim': 768,
        'product_sample_num': 200,  # 减少候选池大小，提高准确率
        'feedback_num': 5,
        'batch_size': 32,  # 增加批次大小
        'epochs': 20,  # 增加训练轮数
        'learning_rate': 0.001,  # 提高学习率
        'reward_model_path': 'output/reward_model_weights.pth',
        'model_path': 'output/emergency_fixed_dqn_model.pth',
        'top_ks': [5, 10, 20, 50]
    }

def emergency_train_single_fold():
    """紧急修复的单折训练"""
    print("=== 紧急修复训练 ===")
    
    config = create_emergency_config()
    
    # 加载数据 - 使用部分数据进行快速验证
    user_history_actions = np.load("./data/user_history_actions.npy", allow_pickle=True)
    # 使用前5000个样本进行快速训练和验证
    user_history_actions = user_history_actions[:5000]
    print(f"使用数据量: {len(user_history_actions)}")
    
    # 创建数据集
    dataset = RecommendationDataset(
        user_history_actions=user_history_actions,
        product_sample_num=config['product_sample_num']
    )
    
    # 分割训练和测试数据
    train_size = int(0.8 * len(dataset))
    test_size = len(dataset) - train_size
    train_dataset, test_dataset = torch.utils.data.random_split(dataset, [train_size, test_size])
    
    train_dataloader = DataLoader(
        train_dataset,
        batch_size=config['batch_size'],
        shuffle=True,
        num_workers=0,
        pin_memory=True,
        drop_last=True
    )
    
    test_dataloader = DataLoader(
        test_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=0,
        pin_memory=True,
        drop_last=True
    )
    
    print(f"训练集大小: {len(train_dataloader)} batches")
    print(f"测试集大小: {len(test_dataloader)} batches")
    
    # 初始化模型
    reward_model = RewardModel(
        history_feature_dim=config['history_dim'],
        product_feature_dim=config['product_dim'],
        output_dim=config['feedback_num']
    ).to(device)
    
    if os.path.exists(config['reward_model_path']):
        reward_model.load_state_dict(torch.load(config['reward_model_path'], map_location=device, weights_only=False))
        print("奖励模型加载成功")
    else:
        print("警告: 未找到奖励模型")
        return
    
    reward_model.eval()
    
    # 创建环境
    max_item_id = dataset.emb_manager.max_item_id
    env = RecommendationEnv(
        config['product_sample_num'],
        reward_model,
        max_item_id,
        device=device,
        history_dim=config['history_dim']
    )
    
    # 创建智能体 - 使用更强的配置
    agent = DQNAgent(
        config['product_sample_num'],
        config['history_dim'],
        config['product_dim'],
        config['batch_size'],
        action_size=config['product_sample_num'],
        device=device,
        gamma=0.99  # 提高折扣因子
    )
    
    # 调整智能体参数
    agent.epsilon = 0.8  # 降低初始探索率
    agent.epsilon_min = 0.05
    agent.epsilon_decay = 0.99
    
    # 使用更高的学习率
    for param_group in agent.optimizer.param_groups:
        param_group['lr'] = config['learning_rate']
    
    print("开始紧急修复训练...")
    
    best_f1 = 0.0
    
    for epoch in range(config['epochs']):
        print(f"\n=== Epoch {epoch + 1}/{config['epochs']} ===")
        
        # 训练阶段
        agent.dqn.train()
        total_loss = 0.0
        total_reward = 0.0
        num_batches = 0
        
        for batch_idx, batch in enumerate(tqdm(train_dataloader, desc=f'训练 Epoch {epoch + 1}')):
            history_actions = batch[0].to(device, dtype=torch.float32)
            product_samples = batch[1].to(device, dtype=torch.float32)
            target_item_ids = batch[2].to(device, dtype=torch.float32)
            candidate_ids_list = batch[4]
            
            state = (history_actions, product_samples)
            
            # 智能体选择动作
            actions = agent.act(state, candidate_ids_list=candidate_ids_list, eval_mode=False)
            
            # 计算归一化商品ID
            batch_size = actions.size(0)
            norm_item_ids = torch.zeros(batch_size, device=device, dtype=torch.float32)
            
            for i in range(batch_size):
                action_idx = actions[i].item()
                if action_idx < len(candidate_ids_list[i]):
                    chosen_item_id = candidate_ids_list[i][action_idx]
                    norm_item_ids[i] = chosen_item_id / float(max_item_id)
                else:
                    chosen_item_id = candidate_ids_list[i][0]
                    norm_item_ids[i] = chosen_item_id / float(max_item_id)
            
            # 环境执行动作
            next_state, rewards, dones, next_candidate_ids_list = env.step(
                state, norm_item_ids, candidate_ids_list
            )
            
            # 存储经验并训练
            agent.remember(state, actions, rewards, next_state, dones, 
                         candidate_ids_list, next_candidate_ids_list)
            loss = agent.replay()
            
            if loss is not None:
                total_loss += loss
                num_batches += 1
            
            total_reward += rewards.mean().item()
        
        if num_batches > 0:
            avg_loss = total_loss / num_batches
            avg_reward = total_reward / len(train_dataloader)
            print(f"训练完成: 平均损失={avg_loss:.4f}, 平均奖励={avg_reward:.4f}, ε={agent.epsilon:.3f}")
        
        # 每2个epoch评估一次
        if (epoch + 1) % 2 == 0:
            print(f"Epoch {epoch + 1} 评估中...")
            
            # 快速评估
            agent.dqn.eval()
            eval_metrics = quick_evaluate(agent, test_dataloader, config['top_ks'], device, max_item_id)
            
            print(f"评估结果:")
            for k in config['top_ks']:
                metrics = eval_metrics[k]
                print(f"  Top-{k}: P={metrics['precision']:.4f}, R={metrics['recall']:.4f}, F1={metrics['f1']:.4f}")
            
            # 保存最佳模型
            current_f1 = eval_metrics[5]['f1']  # 使用Top-5的F1
            if current_f1 > best_f1:
                best_f1 = current_f1
                torch.save(agent.dqn.state_dict(), config['model_path'])
                print(f"🎉 新最佳模型! F1@5: {best_f1:.4f}")
    
    print(f"\n=== 紧急修复训练完成 ===")
    print(f"最佳 F1 分数: {best_f1:.4f}")
    
    return best_f1

def quick_evaluate(agent, dataloader, top_ks, device, max_item_id):
    """快速评估函数"""
    all_metrics = {k: {'precision': [], 'recall': [], 'f1': [], 'ndcg': []} for k in top_ks}
    
    with torch.no_grad():
        for batch in dataloader:
            history_actions = batch[0].to(device, dtype=torch.float32)
            product_samples = batch[1].to(device, dtype=torch.float32)
            candidate_ids_list = batch[4]
            
            state = (history_actions, product_samples)
            
            for k in top_ks:
                recommendations, _ = agent.get_recommendations(state, top_k=k)
                
                for i in range(len(recommendations)):
                    rec_indices = recommendations[i].cpu().numpy()
                    candidate_pool = candidate_ids_list[i]
                    true_item_id = candidate_pool[0]  # 目标商品在第0位
                    
                    if hasattr(true_item_id, 'item'):
                        true_item_id = int(true_item_id.item())
                    else:
                        true_item_id = int(true_item_id)
                    
                    # 转换推荐索引为商品ID
                    rec_item_ids = []
                    for idx in rec_indices[:k]:
                        try:
                            if hasattr(idx, 'item'):
                                idx_val = int(idx.item())
                            else:
                                idx_val = int(idx)
                            
                            if 0 <= idx_val < len(candidate_pool):
                                item_id = candidate_pool[idx_val]
                                if hasattr(item_id, 'item'):
                                    item_id = int(item_id.item())
                                else:
                                    item_id = int(item_id)
                                rec_item_ids.append(item_id)
                            else:
                                fallback_idx = idx_val % len(candidate_pool)
                                item_id = candidate_pool[fallback_idx]
                                if hasattr(item_id, 'item'):
                                    item_id = int(item_id.item())
                                else:
                                    item_id = int(item_id)
                                rec_item_ids.append(item_id)
                        except:
                            item_id = candidate_pool[0]
                            if hasattr(item_id, 'item'):
                                item_id = int(item_id.item())
                            else:
                                item_id = int(item_id)
                            rec_item_ids.append(item_id)
                    
                    # 计算指标
                    true_items = [true_item_id]
                    precision, recall, f1, ndcg = calculate_metrics(rec_item_ids, true_items, k)
                    
                    all_metrics[k]['precision'].append(precision)
                    all_metrics[k]['recall'].append(recall)
                    all_metrics[k]['f1'].append(f1)
                    all_metrics[k]['ndcg'].append(ndcg)
    
    # 计算平均值
    result = {}
    for k in top_ks:
        result[k] = {
            'precision': np.mean(all_metrics[k]['precision']),
            'recall': np.mean(all_metrics[k]['recall']),
            'f1': np.mean(all_metrics[k]['f1']),
            'ndcg': np.mean(all_metrics[k]['ndcg'])
        }
    
    return result

def main():
    """主函数"""
    print("开始紧急修复训练...")
    
    try:
        best_f1 = emergency_train_single_fold()
        
        if best_f1 > 0.01:
            print(f"🎉 紧急修复成功! 最佳F1: {best_f1:.4f}")
            print("建议: 使用修复后的配置继续完整训练")
        else:
            print(f"⚠️ 仍需进一步优化，当前最佳F1: {best_f1:.4f}")
            
    except Exception as e:
        print(f"❌ 紧急修复过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
