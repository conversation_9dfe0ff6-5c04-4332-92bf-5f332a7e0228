# recommendation_env.py
import torch
import numpy as np


class RecommendationEnv:
    def __init__(self, action_size, reward_model, max_item_id, device="cuda", history_dim=768):  # 默认改为768
        self.action_size = action_size
        self.reward_model = reward_model
        self.max_item_id = max_item_id
        self.device = device
        self.max_length = 10
        self.d_model = history_dim  # 直接使用history_dim，现在为768
        self.product_feature_dim = 768
        self.behavior_feature_dim = 5  # 保留用于奖励计算

        # 调试计数器
        self.debug_counter = 0
        self.missing_item_count = 0
        self.total_step_count = 0

        # 修改：使用共享embedding管理器而不是encoded_products
        from utils.data_loader import SharedEmbeddingManager
        self.emb_manager = SharedEmbeddingManager()

        # 创建零向量用于缺失商品
        self.zero_product_feature = torch.zeros(self.product_feature_dim, device=device)
        self.zero_behavior_feature = torch.zeros(self.behavior_feature_dim, device=device)

        # 优化后的奖励值 - 缩小范围，更平衡
        self.reward_map = {
            'none': -0.1,      # 轻微负奖励
            'click': 0.5,      # 小正奖励
            'alipay': 2.0,     # 最高奖励
            'cart': 1.5,       # 高奖励
            'collect': 1.0,    # 中等奖励
        }

        # 7种行为类型映射
        self.action_map = {
            0: 'none',
            1: 'click',
            2: 'alipay',
            3: 'cart',
            4: 'collect',

        }

        # 行为类型到特征位置的映射
        self.action_to_feature = {
            0: 0,
            1: 1,
            2: 2,
            3: 3,
            4: 4,
        }

        self.device = torch.device(device)

    def step(self, state, norm_item_ids, candidate_ids_list):
        """
        candidate_ids_list: 当前状态的候选商品池（从data_loader传入）
        返回: next_state, rewards, dones, next_candidate_ids_list
        """
        history_actions_tensor, product_sample_tensor = state
        batch_size = history_actions_tensor.shape[0]

        # 调试信息
        self.total_step_count += 1
        should_debug = self.total_step_count <= 5

        if should_debug:
            print(f"\n=== 环境Step调试 (第{self.total_step_count}次) ===")
            print(f"输入norm_item_ids: {norm_item_ids[:3]}")

        # 反归一化商品ID
        actual_item_ids = []
        for i in range(batch_size):
            norm_id = norm_item_ids[i].item()
            item_id = int(round(norm_id * self.max_item_id))
            actual_item_ids.append(item_id)

            # 检查商品是否存在
            if item_id not in self.emb_manager.valid_product_ids:
                self.missing_item_count += 1
                if should_debug:
                    print(f"  样本{i}: 商品ID {item_id} 不在编码库中 (norm={norm_id:.4f})")
            else:
                if should_debug:
                    print(f"  样本{i}: 商品ID {item_id} 找到 (norm={norm_id:.4f})")

        # 预测用户反馈 - 利用置信度优化奖励
        with torch.no_grad():
            user_feedback = self.reward_model(
                history_actions_tensor,
                product_sample_tensor,
                norm_item_ids
            )
            # 获取预测概率和最大概率动作
            feedback_probs = torch.softmax(user_feedback, dim=1)
            predicted_actions = torch.argmax(user_feedback, dim=1)
            max_probs = torch.max(feedback_probs, dim=1)[0]  # 获取最大概率值

        # 计算奖励 - 结合置信度
        rewards = []
        action_names = []
        for i, pred in enumerate(predicted_actions):
            action_name = self.action_map.get(pred.item(), 'none')
            base_reward = self.reward_map.get(action_name, -0.1)

            # 置信度调整：低置信度时减少奖励幅度
            confidence = max_probs[i].item()
            confidence_factor = 0.5 + 0.5 * confidence  # 0.5-1.0范围

            adjusted_reward = base_reward * confidence_factor
            rewards.append(adjusted_reward)
            action_names.append(action_name)

        rewards = torch.tensor(rewards, device=self.device, dtype=torch.float32)

        # 更新历史状态
        new_history_actions = []
        for i in range(batch_size):
            current_history = history_actions_tensor[i]
            item_id = actual_item_ids[i]

            # 获取商品特征
            if item_id in self.emb_manager.valid_product_ids:
                product_feature = self.emb_manager.get_embedding(item_id).to(self.device)
            else:
                if self.emb_manager.valid_product_ids:
                    random_item_id = np.random.choice(list(self.emb_manager.valid_product_ids))
                    product_feature = self.emb_manager.get_embedding(random_item_id).to(self.device)
                else:
                    product_feature = self.zero_product_feature.clone()

            # 不再添加行为特征，直接使用商品嵌入
            new_action_feature = product_feature.flatten()

            # 确保新特征维度正确
            if new_action_feature.size(0) != self.d_model:
                if new_action_feature.size(0) < self.d_model:
                    padding = torch.zeros(self.d_model - new_action_feature.size(0), device=self.device)
                    new_action_feature = torch.cat([new_action_feature, padding])
                else:
                    new_action_feature = new_action_feature[:self.d_model]

            updated_history = torch.cat([current_history[1:], new_action_feature.unsqueeze(0)])
            new_history_actions.append(updated_history)

        new_history_actions_tensor = torch.stack(new_history_actions)

        # 转换candidate_ids_list为正确格式
        next_candidate_ids_list = []
        for i in range(batch_size):
            if isinstance(candidate_ids_list[i], torch.Tensor):
                current_candidate_ids = candidate_ids_list[i].cpu().numpy().tolist()
            elif isinstance(candidate_ids_list[i], np.ndarray):
                current_candidate_ids = candidate_ids_list[i].tolist()
            else:
                current_candidate_ids = list(candidate_ids_list[i])

            # 环境不改变候选商品池，直接使用当前的
            # 如果需要动态调整候选池，应该在data_loader中实现
            next_candidate_ids_list.append(current_candidate_ids)

        # 重建next_product_sample_tensor用于返回状态
        next_product_sample_tensor = self._build_product_tensor_from_ids(next_candidate_ids_list)
        next_state = (new_history_actions_tensor, next_product_sample_tensor)

        # 终止条件
        dones = torch.zeros(batch_size, device=self.device, dtype=torch.bool)
        dones[predicted_actions == 2] = True  # alipay
        dones[predicted_actions == 6] = True  # negative

        return next_state, rewards, dones.bool(), next_candidate_ids_list

    def _build_product_tensor_from_ids(self, candidate_ids_list, product_sample_num=1000):
        """从商品ID列表重建product tensor"""
        batch_size = len(candidate_ids_list)
        product_features = []

        for candidate_ids in candidate_ids_list:
            batch_features = []
            for item_id in candidate_ids:
                if item_id == 0 or item_id not in self.emb_manager.valid_product_ids:
                    feature = torch.zeros(self.product_feature_dim, dtype=torch.float32, device=self.device)
                else:
                    feature = self.emb_manager.get_embedding(item_id).to(self.device)
                batch_features.append(feature)

            # 确保大小正确
            while len(batch_features) < product_sample_num:
                batch_features.append(torch.zeros(self.product_feature_dim, dtype=torch.float32, device=self.device))

            if len(batch_features) > product_sample_num:
                batch_features = batch_features[:product_sample_num]

            product_features.append(torch.stack(batch_features))

        return torch.stack(product_features)