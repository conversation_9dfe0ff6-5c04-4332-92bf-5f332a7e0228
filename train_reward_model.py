import torch
import numpy as np
import time
from torch.utils.data import DataLoader
from models.reward_model import RewardModel
from utils.data_loader import RecommendationDataset
import gc
import os
import torch.multiprocessing as mp
from tqdm import tqdm
import torch.cuda.amp as amp

device = "cuda" if torch.cuda.is_available() else "cpu"
print(f"使用设备: {device}")

def train(model, dataloader, epochs, learning_rate, model_path):
    criterion = torch.nn.CrossEntropyLoss().to(device)
    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, 'max', patience=2, factor=0.5)
    scaler = amp.GradScaler(enabled=(device == "cuda"))

    model.to(device)
    total_batches = len(dataloader) * epochs
    start_time = time.time()
    total_samples = epochs * len(dataloader) * dataloader.batch_size

    best_acc = 0.0
    for epoch in range(epochs):
        epoch_loss = 0.0
        correct = 0
        total = 0
        batch_time = time.time()

        # 详细的进度描述
        progress_desc = (f"Epoch {epoch + 1}/{epochs} | "
                         f"Samples: {len(dataloader.dataset)} | "
                         f"Batches: {len(dataloader)}")

        with tqdm(total=len(dataloader), desc=progress_desc) as pbar:
            for idx, batch in enumerate(dataloader):
                # 记录数据加载后时间
                data_time = time.time() - batch_time
                # 将数据移动到GPU
                history_actions_tensor = batch[0].to(device, non_blocking=True)
                product_sample_tensor = batch[1].to(device, non_blocking=True)
                recommend_item_id_norm = batch[2].to(device, non_blocking=True)
                label = batch[3].to(device, non_blocking=True)
                to_device_time = time.time() - batch_time - data_time

                optimizer.zero_grad(set_to_none=True)

                # 前向传播
                with amp.autocast(enabled=(device == "cuda")):
                    output = model(history_actions_tensor, product_sample_tensor, recommend_item_id_norm)
                    loss = criterion(output, label)
                forward_time = time.time() - batch_time - data_time - to_device_time

                # 反向传播
                scaler.scale(loss).backward()
                scaler.step(optimizer)
                scaler.update()
                backward_time = time.time() - batch_time - data_time - to_device_time - forward_time

                epoch_loss += loss.item()
                _, predicted = torch.max(output.data, 1)
                total += label.size(0)
                correct += (predicted == label).sum().item()

                # 每10个批次显示详细信息
                if idx % 10 == 0:
                    elapsed = time.time() - start_time
                    samples_done = epoch * len(dataloader) * dataloader.batch_size + idx * dataloader.batch_size
                    progress_pct = samples_done / total_samples * 100
                    acc = 100 * correct / (total + 1e-8)

                    if device == "cuda":
                        mem_alloc = torch.cuda.memory_allocated() / (1024 ** 3)
                        mem_cached = torch.cuda.memory_reserved() / (1024 ** 3)
                        gpu_info = f" | GPU: {mem_alloc:.2f}/{mem_cached:.2f} GB"
                    else:
                        gpu_info = ""

                    pbar.write(f"Epoch: {epoch + 1}/{epochs}, Batch: {idx}/{len(dataloader)}, "
                               f"Loss: {loss.item():.6f}, Acc: {acc:.2f}%, "
                               f"DataTime: {data_time:.4f}s, ToDeviceTime: {to_device_time:.4f}s, "
                               f"ForwardTime: {forward_time:.4f}s, BackwardTime: {backward_time:.4f}s"
                               f"{gpu_info}")

                    # 显示样本处理进度
                    pbar.set_postfix({
                        'loss': f"{loss.item():.4f}",
                        'acc': f"{acc:.1f}%",
                        'progress': f"{progress_pct:.1f}%",
                        'samples': f"{samples_done}/{total_samples}",
                        'time': f"{elapsed / 60:.1f}min"
                    })

                # 显示GPU内存使用情况
                if torch.cuda.is_available() and idx % 20 == 0:
                    mem_alloc = torch.cuda.memory_allocated() / (1024 ** 3)
                    mem_cached = torch.cuda.memory_reserved() / (1024 ** 3)
                    pbar.write(f"GPU内存: 已分配 {mem_alloc:.2f}GB/已缓存 {mem_cached:.2f}GB | "
                               f"批次 {idx}/{len(dataloader)}")

                pbar.update(1)
                # 重置批次时间
                batch_time = time.time()
                del history_actions_tensor, product_sample_tensor, recommend_item_id_norm, label, output, loss
                gc.collect()
                if device == "cuda":
                    torch.cuda.empty_cache()

        acc = 100 * correct / (total + 1e-8)
        scheduler.step(acc)

        if acc > best_acc:
            best_acc = acc
            torch.save(model.state_dict(), model_path)
            print(f"新最佳模型保存，准确率: {acc:.2f}%")

        torch.save(model.state_dict(), f"{model_path}.epoch{epoch + 1}")
        print(f"Epoch {epoch + 1} 完成 | 准确率: {acc:.2f}% | "
              f"总时间: {(time.time() - start_time) / 60:.1f}分钟")

    print(f"训练完成! 最佳准确率: {best_acc:.2f}% | "
          f"总耗时: {(time.time() - start_time) / 60:.1f}分钟")

def main():
    start_time = time.time()
    print(f"开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")

    if torch.cuda.is_available():
        mp.set_start_method('spawn', force=True)

    torch.manual_seed(42)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(42)
        torch.cuda.empty_cache()


    learning_rate = 0.001
    batch_size = 64 if torch.cuda.is_available() else 16
    epochs = 5
    model_path = 'output/reward_model_weights.pth'
    feedback_num = 5  # 5种反馈类型
    product_sample_num = 1000

    print("加载数据...")
    # encoded_products = np.load("./data/products_embedding.npy", allow_pickle=True).item()
    user_history_actions = np.load("./data/user_history_actions.npy", allow_pickle=True)
    #user_history_actions = user_history_actions[:300]

    print(f"总样本数: {len(user_history_actions)}")
    dataset = RecommendationDataset(
        user_history_actions=user_history_actions,
        product_sample_num=product_sample_num
    )
    print(f"数据集初始化完成: {len(dataset)} 个样本")

    # Windows下设置num_workers=0避免多进程问题
    num_workers = 0
    print(f"使用 {num_workers} 个数据加载工作线程 (避免Windows多进程问题)")

    pin_memory = device == "cuda"
    dataloader = DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=pin_memory,
        drop_last=True
    )

    print(f"数据加载器准备完成: {len(dataloader)} 批次 (每批 {batch_size} 个样本)")
    print(f"数据加载时间: {time.time() - start_time:.1f}秒")

    # 修改这里：使用正确的特征维度创建模型
    model = RewardModel(
        history_feature_dim=768,  # 历史行为特征维度改为768维 移除行为维度独热编码
        product_feature_dim=768,  # 商品特征维度
        output_dim=feedback_num   # 输出5种反馈类型
    )
    print(f"模型初始化: history_feature_dim=768, product_feature_dim=768, output_dim={feedback_num}")
    print(f"训练配置: 批量大小={batch_size}, 周期数={epochs}, 学习率={learning_rate}")

    if device == "cuda":
        print(f"GPU: {torch.cuda.get_device_name(0)}")
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"PyTorch版本: {torch.__version__}")
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024 ** 3:.1f} GB")

    print("开始训练...")
    train(model, dataloader, epochs, learning_rate, model_path)

    total_time = time.time() - start_time
    print(f"训练完成! 总耗时: {total_time / 60:.1f} 分钟")
    print(f"结束时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()