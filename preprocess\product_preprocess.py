import pandas as pd
import numpy as np
import torch
import torch.nn.functional as F
from transformers import BertTokenizer, BertModel

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")


def load_product_data(file_path):
    # 修改编码为 utf-8
    df = pd.read_csv(file_path, encoding='utf-8')
    return df


def encode_products_with_bert(df, tokenizer, bert_model):
    bert_model.to(device)
    bert_model.eval()  # 确保在评估模式

    def encode_text(text):
        with torch.no_grad():
            inputs = tokenizer(text, return_tensors="pt", padding=True, truncation=True, max_length=512).to(device)
            outputs = bert_model(**inputs)
            # 添加严格的L2归一化
            embeddings = outputs.last_hidden_state[:, 0, :].squeeze()

            # 确保所有向量都进行归一化
            if embeddings.dim() == 1:
                embeddings = embeddings.unsqueeze(0)

            # 严格的L2归一化
            embeddings = F.normalize(embeddings, p=2, dim=1)
            return embeddings.cpu().numpy()

    # 合并标题和评论
    df['combined_text'] = df['title'] + ' ' + df['feedback']
    encoded_products = {}
    for _, row in df.iterrows():
        text = row['combined_text']
        item_id = row["item_id"]
        try:
            encoded_text = encode_text(text)
            # 检查范围
            if np.max(np.abs(encoded_text)) > 1.5:
                print(f"警告: 商品ID {item_id} 特征范围异常: {np.min(encoded_text):.4f} 到 {np.max(encoded_text):.4f}")
            encoded_products[item_id] = encoded_text
        except Exception as e:
            print(f"编码商品 {item_id} 时出错: {str(e)}")
            # 使用零向量作为后备
            encoded_products[item_id] = np.zeros(768, dtype=np.float32)

    return encoded_products