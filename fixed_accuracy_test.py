#!/usr/bin/env python3
"""
修复准确率计算问题的最终测试
"""

import torch
import numpy as np
import os
import time
from torch.utils.data import DataLoader
from models.reward_model import RewardModel
from models.recommendation_env import RecommendationEnv
from optimized_dqn_agent import OptimizedDQNAgent, create_optimized_training_loop
from utils.data_loader import RecommendationDataset
from utils.metrics import calculate_metrics
from tqdm import tqdm

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"使用设备: {device}")

def fixed_recommendation_accuracy_test():
    """修复后的推荐准确率测试"""
    print("\n=== 修复后的推荐准确率测试 ===")
    
    # 配置参数
    config = {
        'history_dim': 768,
        'product_dim': 768,
        'product_sample_num': 100,
        'feedback_num': 5,
        'batch_size': 8,
        'reward_model_path': 'output/reward_model_weights.pth'
    }
    
    # 加载数据
    user_history_actions = np.load("./data/user_history_actions.npy", allow_pickle=True)
    user_history_actions = user_history_actions[:50]  # 50个样本测试
    
    dataset = RecommendationDataset(
        user_history_actions=user_history_actions,
        product_sample_num=config['product_sample_num']
    )
    
    dataloader = DataLoader(
        dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=0,
        pin_memory=True,
        drop_last=True
    )
    
    # 初始化优化的智能体
    agent = OptimizedDQNAgent(
        config['product_sample_num'],
        config['history_dim'],
        config['product_dim'],
        config['batch_size'],
        action_size=config['product_sample_num'],
        device=device,
        gamma=0.95
    )
    
    print("开始修复后的准确率测试...")
    
    # 评估准确率
    all_metrics = {'precision': [], 'recall': [], 'f1': [], 'ndcg': []}
    target_in_pool_count = 0
    valid_recommendation_count = 0
    total_samples = 0
    exact_match_count = 0
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(tqdm(dataloader, desc="评估准确率")):
            history_actions = batch[0].to(device, dtype=torch.float32)
            product_samples = batch[1].to(device, dtype=torch.float32)
            target_item_ids = batch[2].cpu().numpy()
            candidate_ids_list = batch[4]
            
            # 获取推荐
            state = (history_actions, product_samples)
            recommendations, q_values = agent.get_recommendations(state, top_k=10)
            
            max_item_id = dataset.emb_manager.max_item_id
            
            # 计算指标
            for i in range(len(recommendations)):
                rec_indices = recommendations[i].cpu().numpy()
                
                # 修复：直接使用原始目标商品ID，而不是通过归一化计算
                # 从候选池第一个位置获取真实目标商品ID（因为我们确保目标商品在第一位）
                candidate_pool = candidate_ids_list[i]
                true_item_id = candidate_pool[0]  # 目标商品确保在第一位
                
                # 处理numpy类型
                if hasattr(true_item_id, 'item'):
                    true_item_id = int(true_item_id.item())
                else:
                    true_item_id = int(true_item_id)
                
                # 检查目标商品是否在候选池中
                target_in_pool_count += 1  # 因为我们确保目标在第一位
                
                # 将推荐索引转换为商品ID - 修复版本
                rec_item_ids = []
                valid_recs = 0
                
                for idx in rec_indices[:5]:  # Top-5
                    try:
                        if hasattr(idx, 'item'):
                            idx_val = int(idx.item())
                        else:
                            idx_val = int(idx)
                        
                        # 确保索引在有效范围内
                        if 0 <= idx_val < len(candidate_pool):
                            item_id = candidate_pool[idx_val]
                            # 处理numpy类型
                            if hasattr(item_id, 'item'):
                                item_id = int(item_id.item())
                            else:
                                item_id = int(item_id)
                            rec_item_ids.append(item_id)
                            valid_recs += 1
                        else:
                            # 索引越界时，使用模运算回到有效范围
                            fallback_idx = idx_val % len(candidate_pool)
                            item_id = candidate_pool[fallback_idx]
                            if hasattr(item_id, 'item'):
                                item_id = int(item_id.item())
                            else:
                                item_id = int(item_id)
                            rec_item_ids.append(item_id)
                            valid_recs += 1
                    except Exception as e:
                        # 异常情况下使用第一个商品
                        item_id = candidate_pool[0]
                        if hasattr(item_id, 'item'):
                            item_id = int(item_id.item())
                        else:
                            item_id = int(item_id)
                        rec_item_ids.append(item_id)
                        valid_recs += 1
                
                if valid_recs > 0:
                    valid_recommendation_count += 1
                
                # 检查是否有精确匹配
                if true_item_id in rec_item_ids:
                    exact_match_count += 1
                
                # 计算指标
                true_items = [true_item_id]
                precision, recall, f1, ndcg = calculate_metrics(rec_item_ids, true_items, 5)
                
                all_metrics['precision'].append(precision)
                all_metrics['recall'].append(recall)
                all_metrics['f1'].append(f1)
                all_metrics['ndcg'].append(ndcg)
                
                total_samples += 1
                
                # 打印前几个样本的详细信息
                if total_samples <= 5:
                    print(f"\n样本 {total_samples}:")
                    print(f"  目标商品ID: {true_item_id}")
                    print(f"  推荐索引: {rec_indices[:5]}")
                    print(f"  推荐商品ID: {rec_item_ids}")
                    print(f"  有效推荐数: {valid_recs}/5")
                    print(f"  精确匹配: {true_item_id in rec_item_ids}")
                    print(f"  指标 - P: {precision:.4f}, R: {recall:.4f}, F1: {f1:.4f}")
    
    # 计算平均指标
    avg_metrics = {
        'precision': np.mean(all_metrics['precision']),
        'recall': np.mean(all_metrics['recall']),
        'f1': np.mean(all_metrics['f1']),
        'ndcg': np.mean(all_metrics['ndcg'])
    }
    
    print(f"\n=== 修复后测试结果 ===")
    print(f"总样本数: {total_samples}")
    print(f"目标在候选池: {target_in_pool_count}/{total_samples} (100.0%)")
    print(f"有效推荐样本: {valid_recommendation_count}/{total_samples} ({valid_recommendation_count/total_samples*100:.1f}%)")
    print(f"精确匹配样本: {exact_match_count}/{total_samples} ({exact_match_count/total_samples*100:.1f}%)")
    print(f"平均准确率 (Top-5):")
    print(f"  精确率: {avg_metrics['precision']:.4f}")
    print(f"  召回率: {avg_metrics['recall']:.4f}")
    print(f"  F1值: {avg_metrics['f1']:.4f}")
    print(f"  NDCG: {avg_metrics['ndcg']:.4f}")
    
    return avg_metrics

def train_and_evaluate_system():
    """训练并评估系统"""
    print("\n=== 训练并评估系统 ===")
    
    # 先测试修复效果
    initial_metrics = fixed_recommendation_accuracy_test()
    
    if initial_metrics['f1'] > 0 or initial_metrics['precision'] > 0:
        print(f"\n✅ 修复成功！初始F1: {initial_metrics['f1']:.4f}")
        print("开始训练以进一步提升准确率...")
        
        # 配置参数
        config = {
            'history_dim': 768,
            'product_dim': 768,
            'product_sample_num': 100,
            'feedback_num': 5,
            'batch_size': 8,
            'reward_model_path': 'output/reward_model_weights.pth'
        }
        
        # 加载数据
        user_history_actions = np.load("./data/user_history_actions.npy", allow_pickle=True)
        user_history_actions = user_history_actions[:50]
        
        dataset = RecommendationDataset(
            user_history_actions=user_history_actions,
            product_sample_num=config['product_sample_num']
        )
        
        dataloader = DataLoader(
            dataset,
            batch_size=config['batch_size'],
            shuffle=True,
            num_workers=0,
            pin_memory=True,
            drop_last=True
        )
        
        # 初始化模型
        agent = OptimizedDQNAgent(
            config['product_sample_num'],
            config['history_dim'],
            config['product_dim'],
            config['batch_size'],
            action_size=config['product_sample_num'],
            device=device,
            gamma=0.95
        )
        
        # 初始化奖励模型和环境
        reward_model = RewardModel(
            history_feature_dim=config['history_dim'],
            product_feature_dim=config['product_dim'],
            output_dim=config['feedback_num']
        ).to(device)
        
        if os.path.exists(config['reward_model_path']):
            reward_model.load_state_dict(torch.load(config['reward_model_path'], map_location=device, weights_only=False))
        
        reward_model.eval()
        
        max_item_id = dataset.emb_manager.max_item_id
        env = RecommendationEnv(
            config['product_sample_num'],
            reward_model,
            max_item_id,
            device=device,
            history_dim=config['history_dim']
        )
        
        # 创建训练循环
        train_optimized_dqn = create_optimized_training_loop()
        
        # 训练模型
        print("开始训练...")
        trained_agent = train_optimized_dqn(agent, dataloader, env, epochs=10, device=device)
        
        # 测试训练后的效果
        print("\n=== 训练后最终测试 ===")
        final_metrics = fixed_recommendation_accuracy_test()
        
        print(f"\n=== 训练效果对比 ===")
        print(f"训练前 F1: {initial_metrics['f1']:.4f}")
        print(f"训练后 F1: {final_metrics['f1']:.4f}")
        print(f"F1 提升: {final_metrics['f1'] - initial_metrics['f1']:.4f}")
        print(f"训练前精确率: {initial_metrics['precision']:.4f}")
        print(f"训练后精确率: {final_metrics['precision']:.4f}")
        
        # 保存训练后的模型
        trained_agent.save_model('output/final_optimized_dqn_model.pth')
        print("最终优化模型已保存")
        
        return final_metrics
    else:
        print("\n❌ 修复仍未成功")
        return initial_metrics

def main():
    """主函数"""
    print("开始最终的准确率修复测试...")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    
    start_time = time.time()
    
    try:
        final_metrics = train_and_evaluate_system()
        
        print(f"\n=== 最终结果 ===")
        print(f"最终 F1 分数: {final_metrics['f1']:.4f}")
        print(f"最终精确率: {final_metrics['precision']:.4f}")
        print(f"最终召回率: {final_metrics['recall']:.4f}")
        print(f"最终 NDCG: {final_metrics['ndcg']:.4f}")
        
        if final_metrics['f1'] > 0.1:
            print("🎉 优化大成功！准确率显著提升！")
        elif final_metrics['f1'] > 0.01:
            print("✅ 优化成功！准确率有明显提升")
        elif final_metrics['f1'] > 0:
            print("✅ 修复成功！准确率从0提升")
        else:
            print("⚠️ 仍需进一步优化")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    total_time = time.time() - start_time
    print(f"\n总测试时间: {total_time:.1f}秒")

if __name__ == "__main__":
    main()
