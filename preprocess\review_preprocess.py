import pandas as pd


def load_review_data(file_path):
    # 使用 read_csv 代替 read_excel，并尝试多种编码
    try:
        # 尝试 utf-8 编码
        df = pd.read_csv(file_path, encoding='utf-8')
    except UnicodeDecodeError:
        try:
            # 如果 utf-8 失败，尝试 gbk 编码
            df = pd.read_csv(file_path, encoding='gbk')
        except UnicodeDecodeError:
            # 如果 gbk 也失败，尝试 latin1 编码
            df = pd.read_csv(file_path, encoding='latin1')

    # 检查必要的列是否存在
    required_columns = ['item_id', 'user_id', 'feedback']
    for col in required_columns:
        if col not in df.columns:
            raise ValueError(f"CSV文件缺少必要的列: {col}")

    # 修复：确保列是字符串类型后再进行字符串操作
    df['item_id'] = df['item_id'].astype(str)
    df['user_id'] = df['user_id'].astype(str)

    # 对DataFrame中的item_id列进行处理，去掉每个值的第一个字符，然后将其转换为整数类型
    df["item_id"] = df['item_id'].astype(int)
    # 对DataFrame中的user_id列进行处理，去掉每个值的第一个字符，然后将其转换为整数类型
    df["user_id"] = df['user_id'].str[1:].astype(int)
    # 对DataFrame中的feedback列进行处理，使用apply方法和lambda函数将每个反馈文本中的空格去除
    df["feedback"] = df["feedback"].apply(lambda x: str(x).replace(" ", ""))

    return df