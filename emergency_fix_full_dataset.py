#!/usr/bin/env python3
"""
紧急修复完整数据集上的准确率问题
"""

import torch
import numpy as np
from torch.utils.data import DataLoader
from utils.data_loader import RecommendationDataset
from tqdm import tqdm

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

def diagnose_full_dataset_issue():
    """诊断完整数据集的问题"""
    print("=== 诊断完整数据集问题 ===")
    
    # 加载完整数据集
    user_history_actions = np.load("./data/user_history_actions.npy", allow_pickle=True)
    print(f"完整数据集大小: {len(user_history_actions)}")
    
    # 创建数据集
    dataset = RecommendationDataset(
        user_history_actions=user_history_actions,
        product_sample_num=1000  # 使用完整的候选池大小
    )
    
    print(f"最大商品ID: {dataset.emb_manager.max_item_id}")
    print(f"有效商品数量: {len(dataset.emb_manager.valid_product_ids)}")
    
    # 随机采样检查
    sample_indices = np.random.choice(len(dataset), 20, replace=False)
    target_in_pool_count = 0
    
    for i in sample_indices:
        try:
            sample = dataset[i]
            history_tensor, product_tensor, target_norm, label, candidate_pool = sample
            
            # 获取原始目标商品ID
            user_data = user_history_actions[i]
            true_target_id = int(user_data['item_id'])
            
            # 检查目标商品是否在候选池中
            is_in_pool = true_target_id in candidate_pool
            if is_in_pool:
                target_in_pool_count += 1
                position = candidate_pool.index(true_target_id)
                print(f"样本 {i}: 目标商品 {true_target_id} 在候选池第 {position} 位")
            else:
                print(f"样本 {i}: ❌ 目标商品 {true_target_id} 不在候选池中")
                print(f"  候选池前5个: {candidate_pool[:5]}")
                
        except Exception as e:
            print(f"样本 {i} 处理出错: {e}")
    
    print(f"\n目标在候选池比例: {target_in_pool_count}/20 = {target_in_pool_count/20*100:.1f}%")
    
    return target_in_pool_count/20

def create_improved_candidate_pool_strategy():
    """创建改进的候选池策略"""
    print("\n=== 创建改进的候选池策略 ===")
    
    # 修复候选池构建逻辑
    improved_code = '''
def _build_candidate_pool(self, history_actions, target_item_id):
    """改进的候选商品池构建 - 确保目标商品100%包含"""
    candidate_pool = []
    total_needed = self.product_sample_num
    
    # 1. 强制添加目标商品到第一个位置
    candidate_pool.append(target_item_id)
    candidate_set = {target_item_id}
    
    # 2. 添加历史商品（增加相关性）
    for action in history_actions:
        item_id = action[0]
        if hasattr(item_id, 'item'):
            item_id = item_id.item()
        else:
            item_id = int(item_id)
            
        if item_id not in candidate_set and len(candidate_pool) < total_needed:
            candidate_pool.append(item_id)
            candidate_set.add(item_id)
    
    # 3. 添加相似商品（基于最近的历史商品）
    recent_items = [action[0] for action in history_actions[-3:]]
    for item_id in recent_items:
        if len(candidate_pool) >= total_needed:
            break
            
        if hasattr(item_id, 'item'):
            item_id = item_id.item()
        else:
            item_id = int(item_id)
        
        try:
            # 获取相似商品，但减少数量以确保多样性
            top_n = min(50, total_needed - len(candidate_pool))
            similar_items = self.emb_manager.get_similar_items(item_id, top_n=top_n)
            
            for sim_item in similar_items:
                if sim_item not in candidate_set and len(candidate_pool) < total_needed:
                    candidate_pool.append(sim_item)
                    candidate_set.add(sim_item)
        except:
            continue
    
    # 4. 添加热门商品
    if len(candidate_pool) < total_needed:
        for item in self.emb_manager.popular_items:
            if item not in candidate_set:
                candidate_pool.append(item)
                candidate_set.add(item)
                if len(candidate_pool) >= total_needed:
                    break
    
    # 5. 随机填充剩余位置
    if len(candidate_pool) < total_needed:
        available_ids = [id for id in self.emb_manager.valid_product_ids 
                        if id not in candidate_set]
        if available_ids:
            needed = total_needed - len(candidate_pool)
            random_sample = random.sample(available_ids, min(needed, len(available_ids)))
            candidate_pool.extend(random_sample)
    
    # 6. 确保大小正确
    candidate_pool = candidate_pool[:total_needed]
    while len(candidate_pool) < total_needed:
        candidate_pool.append(target_item_id)  # 用目标商品填充
    
    # 7. 最终验证
    if target_item_id not in candidate_pool:
        print(f"紧急修复: 强制将目标商品 {target_item_id} 放在第一位")
        candidate_pool[0] = target_item_id
    
    return candidate_pool
'''
    
    print("改进的候选池策略已准备好")
    return improved_code

def main():
    """主函数"""
    print("诊断完整数据集上的准确率问题...")
    
    try:
        # 诊断问题
        target_ratio = diagnose_full_dataset_issue()
        
        if target_ratio < 0.8:
            print(f"\n❌ 发现问题: 目标商品在候选池比例仅 {target_ratio*100:.1f}%")
            print("需要改进候选池构建策略")
            
            # 创建改进策略
            improved_code = create_improved_candidate_pool_strategy()
            print("\n建议立即应用改进的候选池策略")
        else:
            print(f"\n✅ 候选池构建正常: {target_ratio*100:.1f}%")
            print("问题可能在其他地方，需要进一步调试")
            
    except Exception as e:
        print(f"诊断过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
