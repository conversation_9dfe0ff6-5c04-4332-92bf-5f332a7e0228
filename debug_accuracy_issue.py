#!/usr/bin/env python3
"""
调试准确率为0的问题 - 深入分析推荐系统各个环节
"""

import torch
import numpy as np
import os
from torch.utils.data import DataLoader
from models.reward_model import RewardModel
from dqn_agent import DQNAgent
from utils.data_loader import RecommendationDataset
from utils.metrics import calculate_metrics

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"使用设备: {device}")

def debug_candidate_pool_and_target_matching():
    """调试候选池构建和目标商品匹配"""
    print("\n=== 调试1: 候选池构建和目标商品匹配 ===")
    
    # 加载小数据集
    user_history_actions = np.load("./data/user_history_actions.npy", allow_pickle=True)
    user_history_actions = user_history_actions[:20]  # 只用20个样本进行详细调试
    
    dataset = RecommendationDataset(
        user_history_actions=user_history_actions,
        product_sample_num=100
    )
    
    print(f"数据集大小: {len(dataset)}")
    print(f"最大商品ID: {dataset.emb_manager.max_item_id}")
    
    # 检查前5个样本
    target_in_pool_count = 0
    total_samples = 0
    
    for i in range(min(5, len(dataset))):
        print(f"\n--- 样本 {i} ---")
        sample = dataset[i]
        history_tensor, product_tensor, target_norm, label, candidate_pool = sample
        
        # 计算真实目标商品ID
        max_item_id = dataset.emb_manager.max_item_id
        true_item_id = int(target_norm.item() * max_item_id)
        
        print(f"目标商品归一化ID: {target_norm.item():.4f}")
        print(f"目标商品真实ID: {true_item_id}")
        print(f"候选池大小: {len(candidate_pool)}")
        print(f"候选池前10个ID: {candidate_pool[:10]}")
        print(f"候选池后10个ID: {candidate_pool[-10:]}")
        
        # 检查目标商品是否在候选池中
        is_target_in_pool = true_item_id in candidate_pool
        print(f"目标商品是否在候选池中: {is_target_in_pool}")
        
        if is_target_in_pool:
            target_position = candidate_pool.index(true_item_id)
            print(f"目标商品在候选池第 {target_position} 位")
            target_in_pool_count += 1
        
        total_samples += 1
        
        # 检查候选池的商品ID范围
        pool_min = min(candidate_pool)
        pool_max = max(candidate_pool)
        print(f"候选池商品ID范围: {pool_min} - {pool_max}")
        
        # 检查目标商品是否在有效商品范围内
        is_valid_target = true_item_id in dataset.emb_manager.valid_product_ids
        print(f"目标商品是否在有效商品库中: {is_valid_target}")
    
    print(f"\n总结: {target_in_pool_count}/{total_samples} 个样本的目标商品在候选池中")
    print(f"目标商品在候选池比例: {target_in_pool_count/total_samples*100:.1f}%")
    
    return target_in_pool_count/total_samples

def debug_dqn_recommendation_logic():
    """调试DQN推荐逻辑"""
    print("\n=== 调试2: DQN推荐逻辑 ===")
    
    # 配置参数
    config = {
        'history_dim': 768,
        'product_dim': 768,
        'product_sample_num': 100,
        'feedback_num': 5,
        'batch_size': 4,  # 小批次便于调试
        'reward_model_path': 'output/reward_model_weights.pth'
    }
    
    # 加载数据
    user_history_actions = np.load("./data/user_history_actions.npy", allow_pickle=True)
    user_history_actions = user_history_actions[:20]
    
    dataset = RecommendationDataset(
        user_history_actions=user_history_actions,
        product_sample_num=config['product_sample_num']
    )
    
    dataloader = DataLoader(
        dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=0,
        pin_memory=True,
        drop_last=True
    )
    
    # 初始化智能体
    agent = DQNAgent(
        config['product_sample_num'],
        config['history_dim'],
        config['product_dim'],
        config['batch_size'],
        action_size=config['product_sample_num'],
        device=device,
        gamma=0.95
    )
    
    # 测试一个批次
    batch = next(iter(dataloader))
    history_actions = batch[0].to(device, dtype=torch.float32)
    product_samples = batch[1].to(device, dtype=torch.float32)
    target_item_ids = batch[2].cpu().numpy()
    candidate_ids_list = batch[4]
    
    print(f"批次大小: {len(history_actions)}")
    print(f"历史特征形状: {history_actions.shape}")
    print(f"商品特征形状: {product_samples.shape}")
    
    # 获取推荐
    state = (history_actions, product_samples)
    
    # 测试不同的top_k值
    for top_k in [5, 10, 20]:
        print(f"\n--- Top-{top_k} 推荐测试 ---")
        recommendations, q_values = agent.get_recommendations(state, top_k=top_k)

        print(f"推荐结果形状: {recommendations.shape}")
        print(f"Q值形状: {q_values.shape}")
        print(f"Q值范围: [{q_values.min().item():.4f}, {q_values.max().item():.4f}]")
        
        # 分析每个样本的推荐
        max_item_id = dataset.emb_manager.max_item_id
        
        for i in range(len(recommendations)):
            rec_indices = recommendations[i].cpu().numpy()
            true_item_id = int(target_item_ids[i].item() * max_item_id)
            candidate_pool = candidate_ids_list[i]
            
            print(f"\n  样本 {i}:")
            print(f"    目标商品ID: {true_item_id}")
            print(f"    推荐索引: {rec_indices[:5]}")
            print(f"    候选池大小: {len(candidate_pool)}")

            # 将推荐索引转换为商品ID
            rec_item_ids = []
            valid_indices = []
            invalid_indices = []

            for idx in rec_indices[:5]:
                try:
                    if hasattr(idx, 'item'):
                        idx_val = int(idx.item())
                    elif isinstance(idx, np.ndarray):
                        idx_val = int(idx.item())
                    else:
                        idx_val = int(idx)

                    if idx_val < len(candidate_pool):
                        rec_item_ids.append(candidate_pool[idx_val])
                        valid_indices.append(idx_val)
                    else:
                        rec_item_ids.append(-1)
                        invalid_indices.append(idx_val)
                except Exception as e:
                    rec_item_ids.append(-1)
                    invalid_indices.append(f"error: {e}")

            if invalid_indices:
                print(f"    ❌ 无效索引: {invalid_indices}")
            if valid_indices:
                print(f"    ✅ 有效索引: {valid_indices}")
            
            print(f"    推荐商品ID: {rec_item_ids}")
            print(f"    目标商品在推荐中: {true_item_id in rec_item_ids}")
            
            # 计算指标
            true_items = [true_item_id]
            precision, recall, f1, ndcg = calculate_metrics(rec_item_ids, true_items, 5)
            print(f"    指标 - P: {precision:.4f}, R: {recall:.4f}, F1: {f1:.4f}")

def debug_q_values_distribution():
    """调试Q值分布"""
    print("\n=== 调试3: Q值分布分析 ===")
    
    # 配置参数
    config = {
        'history_dim': 768,
        'product_dim': 768,
        'product_sample_num': 100,
        'batch_size': 4
    }
    
    # 加载数据
    user_history_actions = np.load("./data/user_history_actions.npy", allow_pickle=True)
    user_history_actions = user_history_actions[:20]
    
    dataset = RecommendationDataset(
        user_history_actions=user_history_actions,
        product_sample_num=config['product_sample_num']
    )
    
    dataloader = DataLoader(
        dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=0,
        pin_memory=True,
        drop_last=True
    )
    
    # 初始化智能体
    agent = DQNAgent(
        config['product_sample_num'],
        config['history_dim'],
        config['product_dim'],
        config['batch_size'],
        action_size=config['product_sample_num'],
        device=device,
        gamma=0.95
    )
    
    # 测试一个批次
    batch = next(iter(dataloader))
    history_actions = batch[0].to(device, dtype=torch.float32)
    product_samples = batch[1].to(device, dtype=torch.float32)
    
    # 获取Q值
    with torch.no_grad():
        q_values = agent.dqn(history_actions, product_samples)
    
    print(f"Q值张量形状: {q_values.shape}")
    print(f"Q值范围: [{q_values.min().item():.4f}, {q_values.max().item():.4f}]")
    print(f"Q值均值: {q_values.mean().item():.4f}")
    print(f"Q值标准差: {q_values.std().item():.4f}")
    
    # 分析每个样本的Q值分布
    for i in range(min(2, q_values.size(0))):
        sample_q = q_values[i]
        print(f"\n样本 {i} Q值分布:")
        print(f"  最大Q值: {sample_q.max().item():.4f}")
        print(f"  最小Q值: {sample_q.min().item():.4f}")
        print(f"  Top-5 Q值: {torch.topk(sample_q, 5).values.cpu().numpy()}")
        print(f"  Top-5 索引: {torch.topk(sample_q, 5).indices.cpu().numpy()}")

def main():
    """主调试函数"""
    print("开始深入调试准确率为0的问题...")
    
    try:
        # 调试1: 候选池和目标匹配
        target_ratio = debug_candidate_pool_and_target_matching()
        
        # 调试2: DQN推荐逻辑
        debug_dqn_recommendation_logic()
        
        # 调试3: Q值分布
        debug_q_values_distribution()
        
        print("\n=== 调试总结 ===")
        print(f"目标商品在候选池比例: {target_ratio*100:.1f}%")
        
        if target_ratio < 0.5:
            print("❌ 主要问题: 目标商品很少出现在候选池中")
            print("建议: 改进候选池采样策略，确保目标商品有更高概率被包含")
        else:
            print("✅ 候选池构建正常")
            print("❌ 问题可能在于: DQN网络未训练好，或推荐逻辑有误")
            
    except Exception as e:
        print(f"调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
