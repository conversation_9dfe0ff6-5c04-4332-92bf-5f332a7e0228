import torch
import torch.nn as nn
import torch.nn.functional as F
from einops import rearrange


class RewardModel(nn.Module):
    def __init__(self, history_feature_dim=768, product_feature_dim=768, output_dim=5,
                 hidden_dim=256, num_filters=16, filter_sizes=[3, 5], dropout=0.3):
        super().__init__()

        # 确保维度正确
        print(f"RewardModel初始化 - history_dim: {history_feature_dim}, product_dim: {product_feature_dim}")

        # 添加输入归一化层
        self.history_norm = nn.LayerNorm(history_feature_dim)
        self.product_norm = nn.LayerNorm(product_feature_dim)


        # 历史行为卷积层
        self.convs1 = nn.ModuleList([
            nn.Conv1d(history_feature_dim, num_filters, fs, padding=fs // 2)
            for fs in filter_sizes
        ])

        # 商品特征卷积层
        self.convs2 = nn.ModuleList([
            nn.Conv1d(product_feature_dim, num_filters, fs, padding=fs // 2)
            for fs in filter_sizes
        ])

        # 特征融合层
        total_conv_output = len(filter_sizes) * num_filters * 2
        self.feature_fusion = nn.Sequential(
            nn.Linear(total_conv_output + 1, hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout)
        )

        # 输出层
        self.output_layer = nn.Sequential(
            nn.Linear(hidden_dim, output_dim),
            nn.LogSoftmax(dim=1)
        )

        self.dropout = nn.Dropout(dropout)
        self.init_weights()

    def init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')

    def forward(self, history_input, product_input, item_id):
        batch_size = history_input.size(0)

        # 添加维度检查
        if history_input.size(-1) != self.history_norm.normalized_shape[0]:
            print(
                f"警告: 历史特征维度不匹配 - 期望: {self.history_norm.normalized_shape[0]}, 实际: {history_input.size(-1)}")

        if product_input.size(-1) != self.product_norm.normalized_shape[0]:
            print(
                f"警告: 商品特征维度不匹配 - 期望: {self.product_norm.normalized_shape[0]}, 实际: {product_input.size(-1)}")

        # 应用归一化
        history_input = self.history_norm(history_input)
        product_input = self.product_norm(product_input)

        # 维度重排
        history_input = rearrange(history_input, 'b s c -> b c s')
        product_input = rearrange(product_input, 'b s c -> b c s')

        # 处理历史行为
        history_features = []
        for conv in self.convs1:
            x = F.relu(conv(history_input))
            x = F.adaptive_max_pool1d(x, 1).squeeze(2)
            history_features.append(x)
        history_features = torch.cat(history_features, dim=1)

        # 处理商品特征
        product_features = []
        for conv in self.convs2:
            x = F.relu(conv(product_input))
            x = F.adaptive_max_pool1d(x, 1).squeeze(2)
            product_features.append(x)
        product_features = torch.cat(product_features, dim=1)

        # 拼接特征
        item_id = item_id.view(batch_size, 1)
        combined = torch.cat([history_features, product_features, item_id], dim=1)
        combined = self.dropout(combined)

        # 特征融合
        fused = self.feature_fusion(combined)

        # 输出层
        output = self.output_layer(fused)

        return output