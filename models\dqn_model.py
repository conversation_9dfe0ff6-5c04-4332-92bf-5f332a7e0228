import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.nn import TransformerEncoder, TransformerEncoderLayer


class PositionalEncoding(nn.Module):
    def __init__(self, d_model, dropout=0.1, max_len=5000):
        super(PositionalEncoding, self).__init__()
        self.dropout = nn.Dropout(p=dropout)
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-torch.log(torch.tensor(10000.0)) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        self.register_buffer('pe', pe)

    def forward(self, x):
        x = x + self.pe[:x.size(0), :]
        return self.dropout(x)


class StateEncoder(nn.Module):
    def __init__(self, history_dim, hidden_dim=256, nhead=6, num_layers=2, dropout=0.1):
        super().__init__()
        # 输入维度已经是768，不需要投影
        self.positional_encoding = PositionalEncoding(history_dim, dropout)  # history_dim=768

        # 使用history_dim作为 Transformer 的维度
        encoder_layers = TransformerEncoderLayer(history_dim, nhead, dim_feedforward=hidden_dim, dropout=dropout)
        self.transformer = TransformerEncoder(encoder_layers, num_layers)
        self.attention = nn.MultiheadAttention(history_dim, nhead, dropout=dropout)
        self.output_layer = nn.Sequential(
            nn.Linear(history_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )

    def forward(self, history):
        history = history.permute(1, 0, 2)  # [seq_len, batch_size, history_dim]
        history = self.positional_encoding(history)
        encoded = self.transformer(history)
        attn_out, _ = self.attention(encoded, encoded, encoded)
        state = encoded[-1] + attn_out[-1]  # 取最后一个时间步并融合注意力
        state = self.output_layer(state)  # [batch_size, hidden_dim]
        return state


class ActionEncoder(nn.Module):
    """编码单个商品特征为动作表示"""

    def __init__(self, product_dim, hidden_dim=256):
        super(ActionEncoder, self).__init__()
        self.encoder = nn.Sequential(
            nn.Linear(product_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU()
        )

    def forward(self, product_feature):
        # product_feature: [batch_size, product_dim]
        return self.encoder(product_feature)  # [batch_size, hidden_dim]


class QValueNetwork(nn.Module):
    """基于状态和动作计算Q值"""

    def __init__(self, state_dim, action_dim, hidden_dim=256):
        super(QValueNetwork, self).__init__()
        self.fc = nn.Sequential(
            nn.Linear(state_dim + action_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1)
        )

    def forward(self, state, action):
        # state: [batch_size, state_dim]
        # action: [batch_size, action_dim]
        combined = torch.cat([state, action], dim=1)  # [batch_size, state_dim+action_dim]
        return self.fc(combined).squeeze(1)  # [batch_size]


class EnhancedDQN(nn.Module):
    def __init__(self, history_dim, product_dim, action_size, state_hidden=256, action_hidden=256):
        super().__init__()
        # 明确传递history_dim (775)
        self.state_encoder = StateEncoder(history_dim, state_hidden, nhead=6)
        self.action_encoder = ActionEncoder(product_dim, action_hidden)
        # 修复Q网络输入维度
        self.q_network = QValueNetwork(state_hidden, action_hidden)
        # 初始化权重
        self.init_weights()

    def init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)

    def forward(self, history_input, product_input):
        """
        参数:
        history_input: 用户历史行为 [batch_size, seq_len, history_dim]
        product_input: 候选商品池 [batch_size, num_actions, product_dim]

        返回:
        q_values: 每个候选商品的Q值 [batch_size, num_actions]
        """
        batch_size, num_actions, _ = product_input.shape

        # 编码用户历史状态
        state_rep = self.state_encoder(history_input)  # [batch_size, state_hidden]

        # 编码所有候选商品
        flat_products = product_input.view(-1, product_input.size(-1))  # [batch*num_actions, product_dim]
        action_reps = self.action_encoder(flat_products)  # [batch*num_actions, action_hidden]
        action_reps = action_reps.view(batch_size, num_actions, -1)  # [batch_size, num_actions, action_hidden]

        # 计算每个状态-动作对的Q值
        q_values = []
        for i in range(num_actions):
            # 扩展状态表示以匹配动作数量
            expanded_state = state_rep.unsqueeze(1)  # [batch_size, 1, state_hidden]

            # 计算当前动作的Q值
            q_val = self.q_network(
                expanded_state.squeeze(1),
                action_reps[:, i, :]
            )  # [batch_size]
            q_values.append(q_val)

        q_values = torch.stack(q_values, dim=1)  # [batch_size, num_actions]
        return q_values