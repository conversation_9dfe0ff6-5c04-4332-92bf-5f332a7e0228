#!/usr/bin/env python3
"""
优化后的最小化测试脚本 - GPU版本
测试优化后的奖励函数和训练策略
"""

import torch
import numpy as np
import os
import time
from torch.utils.data import DataLoader
from models.reward_model import RewardModel
from models.recommendation_env import RecommendationEnv
from dqn_agent import DQNAgent
from utils.data_loader import RecommendationDataset
from utils.metrics import calculate_metrics
from tqdm import tqdm

# 强制使用GPU
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"使用设备: {device}")
if device.type == 'cuda':
    print(f"GPU: {torch.cuda.get_device_name(0)}")
    print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")

def test_optimized_reward_system():
    """测试优化后的奖励系统"""
    print("\n=== 测试1: 优化后的奖励系统 ===")
    
    # 配置参数 - 小规模测试
    config = {
        'history_dim': 768,
        'product_dim': 768,
        'product_sample_num': 100,  # 减少到100进行快速测试
        'feedback_num': 5,
        'batch_size': 16,  # 小批次
        'reward_model_path': 'output/reward_model_weights.pth'
    }
    
    # 加载小数据集
    print("加载测试数据...")
    user_history_actions = np.load("./data/user_history_actions.npy", allow_pickle=True)
    # 只使用前100个样本进行快速测试
    user_history_actions = user_history_actions[:100]
    print(f"测试样本数: {len(user_history_actions)}")
    
    dataset = RecommendationDataset(
        user_history_actions=user_history_actions,
        product_sample_num=config['product_sample_num']
    )
    
    dataloader = DataLoader(
        dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=0,
        pin_memory=True,
        drop_last=True
    )
    
    # 初始化奖励模型
    reward_model = RewardModel(
        history_feature_dim=config['history_dim'],
        product_feature_dim=config['product_dim'],
        output_dim=config['feedback_num']
    ).to(device)
    
    if os.path.exists(config['reward_model_path']):
        print(f"加载奖励模型: {config['reward_model_path']}")
        reward_model.load_state_dict(torch.load(config['reward_model_path'], map_location=device))
    else:
        print("警告: 未找到奖励模型权重文件")
        return False
    
    reward_model.eval()
    
    # 创建推荐环境
    max_item_id = dataset.emb_manager.max_item_id
    env = RecommendationEnv(
        config['product_sample_num'],
        reward_model,
        max_item_id,
        device=device,
        history_dim=config['history_dim']
    )
    
    # 测试奖励分布
    print("\n测试奖励分布...")
    reward_stats = {'total': 0, 'positive': 0, 'negative': 0, 'rewards': []}
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(tqdm(dataloader, desc="测试奖励")):
            if batch_idx >= 5:  # 只测试5个批次
                break
                
            history_actions = batch[0].to(device, dtype=torch.float32)
            product_samples = batch[1].to(device, dtype=torch.float32)
            target_item_ids = batch[2].to(device, dtype=torch.float32)
            candidate_ids_list = batch[4]
            
            state = (history_actions, product_samples)
            
            # 环境执行并获取奖励
            next_state, rewards, dones, _ = env.step(state, target_item_ids, candidate_ids_list)
            
            # 统计奖励
            for reward in rewards:
                r = reward.item()
                reward_stats['rewards'].append(r)
                reward_stats['total'] += 1
                if r > 0:
                    reward_stats['positive'] += 1
                else:
                    reward_stats['negative'] += 1
    
    # 打印奖励统计
    rewards_array = np.array(reward_stats['rewards'])
    print(f"\n奖励统计:")
    print(f"  总样本数: {reward_stats['total']}")
    print(f"  正奖励: {reward_stats['positive']} ({reward_stats['positive']/reward_stats['total']*100:.1f}%)")
    print(f"  负奖励: {reward_stats['negative']} ({reward_stats['negative']/reward_stats['total']*100:.1f}%)")
    print(f"  奖励范围: [{rewards_array.min():.3f}, {rewards_array.max():.3f}]")
    print(f"  平均奖励: {rewards_array.mean():.3f}")
    print(f"  奖励标准差: {rewards_array.std():.3f}")
    
    return True

def test_optimized_dqn_training():
    """测试优化后的DQN训练"""
    print("\n=== 测试2: 优化后的DQN训练 ===")
    
    # 配置参数
    config = {
        'history_dim': 768,
        'product_dim': 768,
        'product_sample_num': 100,
        'feedback_num': 5,
        'batch_size': 16,
        'epochs': 2,  # 只训练2个epoch
        'reward_model_path': 'output/reward_model_weights.pth'
    }
    
    # 加载数据
    user_history_actions = np.load("./data/user_history_actions.npy", allow_pickle=True)
    user_history_actions = user_history_actions[:100]  # 小数据集
    
    dataset = RecommendationDataset(
        user_history_actions=user_history_actions,
        product_sample_num=config['product_sample_num']
    )
    
    dataloader = DataLoader(
        dataset,
        batch_size=config['batch_size'],
        shuffle=True,
        num_workers=0,
        pin_memory=True,
        drop_last=True
    )
    
    # 初始化模型
    reward_model = RewardModel(
        history_feature_dim=config['history_dim'],
        product_feature_dim=config['product_dim'],
        output_dim=config['feedback_num']
    ).to(device)
    
    if os.path.exists(config['reward_model_path']):
        reward_model.load_state_dict(torch.load(config['reward_model_path'], map_location=device))
    else:
        print("警告: 未找到奖励模型权重文件")
        return False
    
    reward_model.eval()
    
    # 创建环境和智能体
    max_item_id = dataset.emb_manager.max_item_id
    env = RecommendationEnv(
        config['product_sample_num'],
        reward_model,
        max_item_id,
        device=device,
        history_dim=config['history_dim']
    )
    
    agent = DQNAgent(
        config['product_sample_num'],
        config['history_dim'],
        config['product_dim'],
        config['batch_size'],
        action_size=config['product_sample_num'],
        device=device,
        gamma=0.95
    )
    
    print(f"开始训练 - {config['epochs']} epochs, {len(dataloader)} batches per epoch")
    
    # 训练循环
    training_stats = {'losses': [], 'rewards': [], 'epsilons': []}
    
    for epoch in range(config['epochs']):
        agent.dqn.train()
        epoch_loss = 0.0
        epoch_reward = 0.0
        num_batches = 0
        
        progress_bar = tqdm(dataloader, desc=f'Epoch {epoch + 1}/{config["epochs"]}')
        
        for batch_idx, batch in enumerate(progress_bar):
            history_actions = batch[0].to(device, dtype=torch.float32)
            product_samples = batch[1].to(device, dtype=torch.float32)
            target_item_ids = batch[2].to(device, dtype=torch.float32)
            candidate_ids_list = batch[4]
            
            state = (history_actions, product_samples)
            
            # 智能体选择动作
            actions = agent.act(state, candidate_ids_list=candidate_ids_list, eval_mode=False)
            
            # 计算归一化商品ID
            batch_size = actions.size(0)
            norm_item_ids = torch.zeros(batch_size, device=device, dtype=torch.float32)
            
            for i in range(batch_size):
                action_idx = actions[i].item()
                if action_idx < len(candidate_ids_list[i]):
                    chosen_item_id = candidate_ids_list[i][action_idx]
                    norm_item_ids[i] = chosen_item_id / float(max_item_id)
                else:
                    chosen_item_id = candidate_ids_list[i][0]
                    norm_item_ids[i] = chosen_item_id / float(max_item_id)
            
            # 环境执行动作
            next_state, rewards, dones, next_candidate_ids_list = env.step(
                state, norm_item_ids, candidate_ids_list
            )
            
            # 存储经验并训练
            agent.remember(state, actions, rewards, next_state, dones, 
                         candidate_ids_list, next_candidate_ids_list)
            loss = agent.replay()
            
            if loss is not None:
                epoch_loss += loss
                training_stats['losses'].append(loss)
            
            epoch_reward += rewards.mean().item()
            num_batches += 1
            
            # 更新进度条
            if num_batches > 0:
                progress_bar.set_postfix({
                    'avg_reward': f'{epoch_reward / num_batches:.3f}',
                    'loss': f'{loss if loss is not None else 0:.4f}',
                    'epsilon': f'{agent.epsilon:.3f}'
                })
        
        # 记录统计信息
        if num_batches > 0:
            avg_reward = epoch_reward / num_batches
            avg_loss = epoch_loss / num_batches if epoch_loss > 0 else 0
            training_stats['rewards'].append(avg_reward)
            training_stats['epsilons'].append(agent.epsilon)
            
            print(f"\nEpoch {epoch + 1} 完成:")
            print(f"  平均奖励: {avg_reward:.4f}")
            print(f"  平均损失: {avg_loss:.4f}")
            print(f"  探索率: {agent.epsilon:.3f}")
    
    # 打印训练统计
    print(f"\n训练完成统计:")
    print(f"  最终平均奖励: {training_stats['rewards'][-1]:.4f}")
    print(f"  奖励改善: {training_stats['rewards'][-1] - training_stats['rewards'][0]:.4f}")
    print(f"  最终探索率: {training_stats['epsilons'][-1]:.3f}")
    
    return True

def test_recommendation_accuracy():
    """测试推荐准确率"""
    print("\n=== 测试3: 推荐准确率评估 ===")

    # 配置参数
    config = {
        'history_dim': 768,
        'product_dim': 768,
        'product_sample_num': 100,
        'feedback_num': 5,
        'batch_size': 16,
        'reward_model_path': 'output/reward_model_weights.pth'
    }

    # 加载数据
    user_history_actions = np.load("./data/user_history_actions.npy", allow_pickle=True)
    user_history_actions = user_history_actions[:100]  # 小数据集

    dataset = RecommendationDataset(
        user_history_actions=user_history_actions,
        product_sample_num=config['product_sample_num']
    )

    dataloader = DataLoader(
        dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=0,
        pin_memory=True,
        drop_last=True
    )

    # 初始化模型
    reward_model = RewardModel(
        history_feature_dim=config['history_dim'],
        product_feature_dim=config['product_dim'],
        output_dim=config['feedback_num']
    ).to(device)

    if os.path.exists(config['reward_model_path']):
        reward_model.load_state_dict(torch.load(config['reward_model_path'], map_location=device, weights_only=False))
    else:
        print("警告: 未找到奖励模型权重文件")
        return False

    reward_model.eval()

    # 创建智能体
    max_item_id = dataset.emb_manager.max_item_id
    agent = DQNAgent(
        config['product_sample_num'],
        config['history_dim'],
        config['product_dim'],
        config['batch_size'],
        action_size=config['product_sample_num'],
        device=device,
        gamma=0.95
    )

    # 评估准确率
    print("评估推荐准确率...")
    all_metrics = {'precision': [], 'recall': [], 'f1': [], 'ndcg': []}

    with torch.no_grad():
        for batch_idx, batch in enumerate(tqdm(dataloader, desc="评估准确率")):
            history_actions = batch[0].to(device, dtype=torch.float32)
            product_samples = batch[1].to(device, dtype=torch.float32)
            target_item_ids = batch[2].cpu().numpy()
            candidate_ids_list = batch[4]

            # 获取推荐
            state = (history_actions, product_samples)
            recommendations = agent.get_recommendations(state, top_k=10)

            # 计算指标
            for i in range(len(recommendations)):
                rec_indices = recommendations[i].cpu().numpy()
                true_item_id = int(target_item_ids[i].item() * max_item_id)
                candidate_pool = candidate_ids_list[i]

                # 将推荐索引转换为商品ID
                rec_item_ids = []
                for idx in rec_indices:
                    try:
                        if hasattr(idx, 'item'):
                            idx_val = int(idx.item())
                        elif isinstance(idx, np.ndarray):
                            idx_val = int(idx.item())
                        else:
                            idx_val = int(idx)

                        if idx_val < len(candidate_pool):
                            rec_item_ids.append(candidate_pool[idx_val])
                        else:
                            rec_item_ids.append(-1)  # 无效推荐
                    except:
                        rec_item_ids.append(-1)  # 处理异常情况

                # 计算指标
                true_items = [true_item_id]
                precision, recall, f1, ndcg = calculate_metrics(rec_item_ids[:5], true_items, 5)

                all_metrics['precision'].append(precision)
                all_metrics['recall'].append(recall)
                all_metrics['f1'].append(f1)
                all_metrics['ndcg'].append(ndcg)

    # 计算平均指标
    avg_metrics = {
        'precision': np.mean(all_metrics['precision']),
        'recall': np.mean(all_metrics['recall']),
        'f1': np.mean(all_metrics['f1']),
        'ndcg': np.mean(all_metrics['ndcg'])
    }

    print(f"\n推荐准确率结果 (Top-5):")
    print(f"  精确率 (Precision): {avg_metrics['precision']:.4f}")
    print(f"  召回率 (Recall): {avg_metrics['recall']:.4f}")
    print(f"  F1值: {avg_metrics['f1']:.4f}")
    print(f"  NDCG: {avg_metrics['ndcg']:.4f}")

    return avg_metrics

def main():
    """主测试函数"""
    print("开始优化后的最小化测试...")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")

    if not torch.cuda.is_available():
        print("警告: 未检测到CUDA，将使用CPU运行")

    start_time = time.time()

    try:
        # 测试1: 奖励系统
        success1 = test_optimized_reward_system()

        if success1:
            # 测试2: DQN训练
            success2 = test_optimized_dqn_training()

            if success2:
                # 测试3: 推荐准确率
                accuracy_metrics = test_recommendation_accuracy()

                if accuracy_metrics:
                    print("\n=== 测试完成 ===")
                    print("✅ 优化后的系统测试成功!")
                    print(f"✅ 推荐准确率: F1={accuracy_metrics['f1']:.4f}, Precision={accuracy_metrics['precision']:.4f}")

                    if accuracy_metrics['f1'] > 0.01:  # 如果F1分数大于1%
                        print("🚀 建议: 准确率表现良好，可以进行完整训练")
                    else:
                        print("⚠️  建议: 准确率较低，可能需要进一步调优")
                else:
                    print("\n❌ 准确率测试失败")
            else:
                print("\n❌ DQN训练测试失败")
        else:
            print("\n❌ 奖励系统测试失败")

    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

    total_time = time.time() - start_time
    print(f"\n总测试时间: {total_time:.1f}秒")

if __name__ == "__main__":
    main()
