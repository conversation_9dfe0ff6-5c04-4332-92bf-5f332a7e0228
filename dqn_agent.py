import torch
import torch.optim as optim
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import random
from models.dqn_model import EnhancedDQN


class PrioritizedReplayBuffer:
    def __init__(self, buffer_size, device):
        self.buffer_size = buffer_size
        self.buffer = []
        self.priorities = np.zeros((buffer_size,), dtype=np.float32)
        self.alpha = 0.6
        self.beta = 0.4
        self.epsilon = 1e-5
        self.index = 0
        self.is_full = False
        self.device = device

    def add(self, experience, priority):
        # 经验格式：(history_actions, candidate_ids, action, reward, next_history_actions, next_candidate_ids, done)
        # candidate_ids 和 next_candidate_ids 是商品ID列表，不是embedding tensor

        # 确保数据在CPU上，但candidate_ids保持为python list
        cpu_experience = []
        for i, item in enumerate(experience):
            if i in [1, 5]:  # candidate_ids 和 next_candidate_ids 的位置
                cpu_experience.append(item)  # 保持为list，不转换
            elif isinstance(item, torch.Tensor):
                cpu_experience.append(item.cpu())
            else:
                cpu_experience.append(item)

        cpu_experience = tuple(cpu_experience)

        if self.index >= self.buffer_size:
            self.is_full = True
        if self.is_full:
            idx = self.index % self.buffer_size
            self.buffer[idx] = cpu_experience
            self.priorities[idx] = (priority + self.epsilon) ** self.alpha
        else:
            self.buffer.append(cpu_experience)
            self.priorities[self.index] = (priority + self.epsilon) ** self.alpha
        self.index += 1

    def sample(self, batch_size):
        priorities = self.priorities[:len(self.buffer)] if not self.is_full else self.priorities
        probs = priorities / np.sum(priorities)
        indices = np.random.choice(len(self.buffer), batch_size, p=probs)
        experiences = [self.buffer[i] for i in indices]
        weights = (len(self.buffer) * probs[indices]) ** (-self.beta)
        weights /= weights.max() if weights.max() > 0 else 1.0
        return experiences, weights, indices

    def update_priorities(self, indices, priorities):
        for i, priority in zip(indices, priorities):
            idx = i % self.buffer_size
            self.priorities[idx] = (priority + self.epsilon) ** self.alpha


class DQNAgent:
    def __init__(self, state_size, history_dim, product_dim, batch_size, action_size=1000,
                 device="cuda", gamma=0.95, use_double_dqn=True, debug_freq=50):
        self.state_size = state_size
        self.action_size = action_size
        self.device = device
        self.use_double_dqn = use_double_dqn
        self.debug_freq = debug_freq
        self.replay_count = 0

        self.dqn = EnhancedDQN(history_dim, product_dim, action_size).to(self.device)
        self.target_dqn = EnhancedDQN(history_dim, product_dim, action_size).to(self.device)
        self.target_dqn.load_state_dict(self.dqn.state_dict())

        for param in self.target_dqn.parameters():
            param.requires_grad = False

        # 优化学习率和调度器
        self.optimizer = optim.Adam(self.dqn.parameters(), lr=0.0005, weight_decay=1e-4)
        self.scheduler = optim.lr_scheduler.StepLR(self.optimizer, step_size=500, gamma=0.9)

        self.gamma = gamma
        # 优化探索策略 - 更慢的衰减
        self.epsilon = 1.0
        self.epsilon_min = 0.05  # 保持更多探索
        self.epsilon_decay = 0.998  # 更慢的衰减
        self.batch_size = batch_size
        self.replay_buffer = PrioritizedReplayBuffer(buffer_size=50000, device=self.device)
        self.training_steps = 0

        # 添加共享embedding管理器
        from utils.data_loader import SharedEmbeddingManager
        self.emb_manager = SharedEmbeddingManager()

    def _build_product_tensor_from_ids(self, candidate_ids_list, product_sample_num=1000):
        """从商品ID列表重建product tensor，确保维度正确"""
        batch_size = len(candidate_ids_list)
        product_features = []

        for candidate_ids in candidate_ids_list:
            batch_features = []
            for item_id in candidate_ids:
                if item_id == 0 or item_id not in self.emb_manager.valid_product_ids:
                    feature = torch.zeros(self.emb_manager.item_emb_matrix.shape[1], dtype=torch.float32)
                else:
                    feature = self.emb_manager.get_embedding(item_id)
                batch_features.append(feature)

            # 确保大小正确
            while len(batch_features) < product_sample_num:
                batch_features.append(torch.zeros(self.emb_manager.item_emb_matrix.shape[1], dtype=torch.float32))

            if len(batch_features) > product_sample_num:
                batch_features = batch_features[:product_sample_num]

            product_features.append(torch.stack(batch_features))

        result = torch.stack(product_features).to(self.device)

        # 验证维度
        expected_shape = (batch_size, product_sample_num, self.emb_manager.item_emb_matrix.shape[1])
        if result.shape != expected_shape:
            print(f"Warning: product tensor shape {result.shape} != expected {expected_shape}")

        return result

    def act(self, state, candidate_ids_list=None, eval_mode=False, top_k=50):
        history_actions, product_samples = state
        batch_size = history_actions.shape[0]
        action_size = product_samples.size(1)

        if eval_mode:
            with torch.no_grad():
                q_values = self.dqn(history_actions, product_samples)
                return torch.topk(q_values, min(top_k, action_size), dim=1).indices

        # 改进的探索策略 - epsilon-greedy with softmax exploration
        if random.random() < self.epsilon:
            # 使用softmax探索，而不是完全随机
            with torch.no_grad():
                q_values = self.dqn(history_actions, product_samples)
                # 添加噪声进行探索
                exploration_noise = torch.randn_like(q_values) * 0.1
                noisy_q_values = q_values + exploration_noise
                action_probs = F.softmax(noisy_q_values / 0.5, dim=1)  # 温度参数0.5
                return torch.multinomial(action_probs, 1).squeeze(1)
        else:
            with torch.no_grad():
                q_values = self.dqn(history_actions, product_samples)
                return torch.argmax(q_values, dim=1)

    def replay(self):
        if len(self.replay_buffer.buffer) < self.batch_size:
            return None

        self.replay_count += 1
        experiences, weights, indices = self.replay_buffer.sample(self.batch_size)

        # 解包经验
        history_actions_list = []
        candidate_ids_list = []
        actions_list = []
        rewards_list = []
        next_history_actions_list = []
        next_candidate_ids_list = []
        dones_list = []

        for exp in experiences:
            history_actions_list.append(exp[0].to(self.device))
            candidate_ids_list.append(exp[1])  # 保持为list
            actions_list.append(exp[2].to(self.device))
            rewards_list.append(exp[3].to(self.device))
            next_history_actions_list.append(exp[4].to(self.device))
            next_candidate_ids_list.append(exp[5])  # 保持为list
            dones_list.append(exp[6].to(self.device))

        # 重建product tensors - 确保维度正确
        product_samples = self._build_product_tensor_from_ids(candidate_ids_list, self.action_size)
        next_product_samples = self._build_product_tensor_from_ids(next_candidate_ids_list, self.action_size)

        # 转换为张量
        history_actions = torch.stack(history_actions_list)
        actions = torch.stack(actions_list).long()
        rewards = torch.stack(rewards_list).float()
        next_history = torch.stack(next_history_actions_list)
        dones = torch.stack(dones_list).float()
        weights = torch.FloatTensor(weights).to(self.device)

        # 计算目标Q值
        with torch.no_grad():
            if self.use_double_dqn:
                next_q_current = self.dqn(next_history, next_product_samples)
                next_actions = torch.argmax(next_q_current, dim=1)

                next_q_target = self.target_dqn(next_history, next_product_samples)
                max_next_q = next_q_target.gather(1, next_actions.unsqueeze(1)).squeeze(1)

                if self.replay_count % self.debug_freq == 0:
                    print(f"Double DQN - Selected actions: {next_actions[:5]}")
            else:
                next_q_target = self.target_dqn(next_history, next_product_samples)
                max_next_q = torch.max(next_q_target, dim=1)[0]

                if self.replay_count % self.debug_freq == 0:
                    print(f"Traditional DQN - Max Q values: {max_next_q[:5]}")

            dones_float = dones.float()
            target_q = rewards + (1 - dones_float) * self.gamma * max_next_q

        # 计算当前Q值
        current_q = self.dqn(history_actions, product_samples)
        current_q = current_q.gather(1, actions.unsqueeze(1)).squeeze(1)

        # 计算损失
        td_errors = target_q - current_q
        loss = (weights * (td_errors ** 2)).mean()

        # 优化步骤
        self.optimizer.zero_grad()
        loss.backward()
        torch.nn.utils.clip_grad_norm_(self.dqn.parameters(), 1.0)
        self.optimizer.step()
        self.scheduler.step()

        # 更新优先级
        self.replay_buffer.update_priorities(indices, abs(td_errors.detach().cpu().numpy()))

        # 更新目标网络
        self.training_steps += 1
        if self.training_steps % 100 == 0:
            self.update_target_network()

        # 衰减探索率
        self.epsilon = max(self.epsilon_min, self.epsilon * self.epsilon_decay)

        return loss.item()

    def remember(self, state, action, reward, next_state, done, candidate_ids_list, next_candidate_ids_list):
        try:
            history, products = state
            next_history, next_products = next_state

            # 转换candidate_ids_list为Python list格式
            converted_candidate_ids_list = []
            for candidate_ids in candidate_ids_list:
                if isinstance(candidate_ids, torch.Tensor):
                    converted_candidate_ids_list.append(candidate_ids.cpu().numpy().tolist())
                elif isinstance(candidate_ids, np.ndarray):
                    converted_candidate_ids_list.append(candidate_ids.tolist())
                else:
                    converted_candidate_ids_list.append(list(candidate_ids))

            # 转换next_candidate_ids_list为Python list格式
            converted_next_candidate_ids_list = []
            for candidate_ids in next_candidate_ids_list:
                if isinstance(candidate_ids, torch.Tensor):
                    converted_next_candidate_ids_list.append(candidate_ids.cpu().numpy().tolist())
                elif isinstance(candidate_ids, np.ndarray):
                    converted_next_candidate_ids_list.append(candidate_ids.tolist())
                else:
                    converted_next_candidate_ids_list.append(list(candidate_ids))

            # 为了计算优先级，直接使用原始的products张量
            with torch.no_grad():
                q_values = self.dqn(history, products)
                current_q = q_values.gather(1, action.unsqueeze(1)).squeeze(1)

                if self.use_double_dqn:
                    next_q_current = self.dqn(next_history, next_products)
                    next_actions = torch.argmax(next_q_current, dim=1)

                    next_q_target = self.target_dqn(next_history, next_products)
                    max_next_q = next_q_target.gather(1, next_actions.unsqueeze(1)).squeeze(1)
                else:
                    next_q = self.target_dqn(next_history, next_products)
                    max_next_q = torch.max(next_q, dim=1)[0]

                done_float = done.float()
                expected_q = reward + (1 - done_float) * self.gamma * max_next_q
                priority = abs(expected_q - current_q).detach().cpu().numpy()

            # 存储到缓冲区 - 只存商品ID，不存embedding
            for i in range(len(action)):
                self.replay_buffer.add((
                    history[i],  # history_actions tensor
                    converted_candidate_ids_list[i],  # 商品ID列表
                    action[i],  # action
                    reward[i],  # reward
                    next_history[i],  # next_history_actions tensor
                    converted_next_candidate_ids_list[i],  # 下一状态的商品ID列表
                    done[i]  # done
                ), priority[i])

        except Exception as e:
            print(f"Error in remember: {str(e)}")
            import traceback
            traceback.print_exc()

    def update_target_network(self):
        self.target_dqn.load_state_dict(self.dqn.state_dict())

    def get_recommendations(self, state, top_k=30):
        history_actions, product_samples = state
        action_size = product_samples.size(1)

        with torch.no_grad():
            q_values = self.dqn(history_actions, product_samples)
            sorted_indices = torch.argsort(q_values, dim=1, descending=True)
            return sorted_indices[:, :min(top_k, action_size)], q_values