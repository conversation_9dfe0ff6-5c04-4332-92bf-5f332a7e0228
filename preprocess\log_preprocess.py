import pandas as pd
import torch
import torch.nn as nn


def load_log_data(file_path):
    """
    加载日志数据的函数
    :param file_path: 日志数据文件的路径
    :return: 处理后的日志数据DataFrame
    """
    # 使用pandas的read_csv函数读取指定路径的CSV文件，编码格式为utf-8
    log_df = pd.read_csv(file_path, encoding='utf-8')
    # 以下代码行被注释掉，原本可能用于将vtime列转换为日期时间类型
    # log_df['vtime'] = pd.to_datetime(log_df['vtime'])

    # 修复：确保列是字符串类型后再进行字符串操作
    log_df['item_id'] = log_df['item_id'].astype(str)  # 先转换为字符串
    log_df['user_id'] = log_df['user_id'].astype(str)  # 先转换为字符串

    # 对item_id列进行处理，去掉第一个字符并转换为整数类型
    log_df["item_id"] = log_df['item_id'].astype(int)
    # 对user_id列进行处理，去掉第一个字符并转换为整数类型
    log_df["user_id"] = log_df['user_id'].str[1:].astype(int)
    # 假设我们仅使用用户的动作序列作为历史日志特征，以下代码行被注释掉
    # log_data = log_df.groupby('user_id')['action'].apply(list).reset_index()
    return log_df


def log_data_processed(log_data):
    """
    处理日志数据的函数，将日志数据按用户ID分组，生成每个用户的动作序列
    :param log_data: 日志数据DataFrame
    :return: 包含每个用户动作序列的字典
    """
    # 使用字典推导式，按用户ID分组，将每个用户的item_id和action组成元组列表作为值
    sequences = {user_id: [(item_id, action) for item_id, action in zip(log['item_id'], log['action'])]
                 for user_id, log in log_data.groupby('user_id')}
    return sequences


def encode_logs_with_embedding(logs, action_to_index, d_model=128, num_layers=2):
    """
    对日志数据进行编码的函数，使用Transformer编码器
    :param logs: 日志数据，包含'action'列
    :param action_to_index: 动作到索引的映射字典
    :param d_model: 嵌入维度，默认为128
    :param num_layers: Transformer编码器的层数，默认为2
    :return: 编码后的日志数据
    """

    def pad_sequence(sequences, max_len):
        """
        对序列进行填充的内部函数
        :param sequences: 待填充的序列列表
        :param max_len: 最大长度
        :return: 填充后的序列张量
        """
        # 创建一个全零的张量，形状为(序列数量, 最大长度)，数据类型为long
        padded_sequences = torch.zeros((len(sequences), max_len)).long()
        # 遍历每个序列
        for i, seq in enumerate(sequences):
            # 将序列中每个动作转换为对应的索引，并填充到padded_sequences中
            padded_sequences[i, :len(seq)] = torch.tensor([action_to_index[action] for action in seq[:max_len]]).long()
        return padded_sequences

    # 将日志数据中的'action'列转换为张量列表
    sequences = [torch.tensor(log) for log in logs['action']]
    # 计算序列的最大长度
    max_len = max(len(seq) for seq in sequences)
    # 调用pad_sequence函数对序列进行填充
    padded_sequences = pad_sequence(sequences, max_len)
    # 创建一个嵌入层，输入维度为动作索引的数量，输出维度为d_model
    embedding = nn.Embedding(len(action_to_index), d_model)
    # 创建一个Transformer编码器层，设置嵌入维度和头数
    encoder_layer = nn.TransformerEncoderLayer(d_model=d_model, nhead=8)
    # 创建一个Transformer编码器，设置编码器层和层数
    transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
    # 对填充后的序列进行嵌入，然后通过Transformer编码器进行编码
    encoded_logs = transformer_encoder(embedding(padded_sequences))
    return encoded_logs