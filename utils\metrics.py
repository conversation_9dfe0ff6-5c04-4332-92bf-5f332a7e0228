# metrics.py
import numpy as np


def calculate_dcg(relevance_list, k=None):
    if k is not None:
        relevance_list = relevance_list[:k]
    dcg = 0
    for i, rel in enumerate(relevance_list, 1):
        dcg += (2 ** rel - 1) / np.log2(i + 1)
    return dcg


def calculate_idcg(relevance_list, k=None):
    sorted_relevance = sorted(relevance_list, reverse=True)
    return calculate_dcg(sorted_relevance, k)


def calculate_ndcg(pred_items, true_items, k=None):
    if k is not None:
        pred_items = pred_items[:k]

    # 构建相关性列表
    relevance_list = [1 if item in true_items else 0 for item in pred_items]

    # 保护逻辑：无相关项时返回0
    if sum(relevance_list) == 0:
        return 0.0

    dcg = calculate_dcg(relevance_list, k)
    idcg = calculate_idcg(relevance_list, k)

    return dcg / idcg if idcg > 0 else 0.0


def calculate_metrics(pred_items, true_items, k, debug_info=None):
    pred_k = pred_items[:k]
    true_set = set(true_items)

    # 调试信息
    if debug_info and debug_info.get('debug', False):
        print(f"DEBUG: pred_k={pred_k}, true_set={true_set}")
        print(f"DEBUG: pred_k in true_set: {[item in true_set for item in pred_k]}")
        print(f"DEBUG: true_items: {true_items}")

    # 计算命中数
    hit = sum(1 for item in pred_k if item in true_set)

    # Precision@k
    precision = hit / k if k > 0 else 0.0

    # Recall@k
    recall = hit / len(true_items) if len(true_items) > 0 else 0.0

    # F1@k
    if precision + recall == 0:
        f1 = 0.0
    else:
        f1 = 2 * (precision * recall) / (precision + recall)

    # NDCG@k
    ndcg = calculate_ndcg(pred_items, true_items, k)

    return precision, recall, f1, ndcg