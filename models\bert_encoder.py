#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2025/1/21 10:17
#

# 从 transformers 库中导入 BertTokenizer 和 BertModel
from transformers import BertTokenizer, BertModel


# 定义一个名为 BERTEncoder 的类，用于对文本进行 BERT 编码
class BERTEncoder:
    # 类的初始化方法，用于创建 BERT 分词器和 BERT 模型实例
    def __init__(self):
        # 从预训练的 'bert-base-chinese' 模型中加载分词器
        self.tokenizer = BertTokenizer.from_pretrained('bert-base-chinese')
        # 从预训练的 'bert-base-chinese' 模型中加载 BERT 模型
        self.model = BertModel.from_pretrained('bert-base-chinese')

    # 定义一个名为 encode 的方法，用于对输入的文本进行编码
    def encode(self, text):
        # 使用分词器对输入的文本进行处理，返回 PyTorch 张量，进行填充和截断，最大长度为 512
        inputs = self.tokenizer(text, return_tensors="pt", padding=True, truncation=True, max_length=512)
        # 将处理后的输入传入 BERT 模型，得到输出
        outputs = self.model(**inputs)
        # 从输出中提取最后一层隐藏状态的第一个 token 的表示，并进行压缩
        return outputs.last_hidden_state[:, 0, :].squeeze()