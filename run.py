#!/usr/bin/env python3
"""
DQN推荐系统 - 一键运行脚本
"""

import os
import sys
import subprocess
import time

def check_dependencies():
    """检查依赖包是否安装"""
    print("🔍 检查依赖包...")
    
    required_packages = [
        'torch', 'transformers', 'sklearn', 'pandas', 
        'numpy', 'tqdm', 'einops'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'sklearn':
                import sklearn
            else:
                __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def check_data_files():
    """检查数据文件是否存在"""
    print("\n📁 检查数据文件...")
    
    required_files = [
        "data/user_history_actions.npy",
        "data/item_emb_matrix.npy", 
        "data/item_id_map.npy",
        "data/item_popularity.npy",
        "data/products_embedding.npy"
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            size_mb = os.path.getsize(file_path) / (1024*1024)
            print(f"✓ {file_path} ({size_mb:.1f} MB)")
        else:
            print(f"✗ {file_path} 不存在")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n❌ 缺少数据文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 所有数据文件存在")
    return True

def check_reward_model():
    """检查奖励模型是否存在"""
    print("\n🎯 检查奖励模型...")
    
    reward_model_path = "output/reward_model_weights.pth"
    
    if os.path.exists(reward_model_path):
        size_mb = os.path.getsize(reward_model_path) / (1024*1024)
        print(f"✓ {reward_model_path} ({size_mb:.1f} MB)")
        return True
    else:
        print(f"⚠ {reward_model_path} 不存在")
        return False

def train_reward_model():
    """训练奖励模型"""
    print("\n🚀 开始训练奖励模型...")
    print("这可能需要30-60分钟，请耐心等待...")
    
    try:
        result = subprocess.run([sys.executable, "train_reward_model.py"], 
                              capture_output=True, text=True, timeout=3600)
        
        if result.returncode == 0:
            print("✅ 奖励模型训练完成")
            return True
        else:
            print(f"❌ 奖励模型训练失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 奖励模型训练超时")
        return False
    except Exception as e:
        print(f"❌ 奖励模型训练出错: {e}")
        return False

def train_dqn_model():
    """训练DQN模型"""
    print("\n🎯 开始训练DQN推荐模型...")
    print("这可能需要20-40分钟，请耐心等待...")
    
    try:
        # 实时显示输出
        process = subprocess.Popen([sys.executable, "train.py"], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.STDOUT,
                                 universal_newlines=True,
                                 bufsize=1)
        
        # 实时打印输出
        for line in process.stdout:
            print(line.rstrip())
        
        process.wait()
        
        if process.returncode == 0:
            print("\n✅ DQN模型训练完成")
            return True
        else:
            print(f"\n❌ DQN模型训练失败，返回码: {process.returncode}")
            return False
            
    except Exception as e:
        print(f"❌ DQN模型训练出错: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 DQN推荐系统 - 一键运行脚本")
    print("=" * 60)
    
    # 1. 检查依赖
    if not check_dependencies():
        return
    
    # 2. 检查数据文件
    if not check_data_files():
        return
    
    # 3. 检查奖励模型
    if not check_reward_model():
        print("\n❓ 是否训练奖励模型? (y/n): ", end="")
        choice = input().lower().strip()
        
        if choice in ['y', 'yes', '是']:
            if not train_reward_model():
                print("❌ 奖励模型训练失败，无法继续")
                return
        else:
            print("❌ 需要奖励模型才能训练DQN，程序退出")
            return
    
    # 4. 训练DQN模型
    print("\n❓ 是否开始训练DQN模型? (y/n): ", end="")
    choice = input().lower().strip()
    
    if choice in ['y', 'yes', '是']:
        if train_dqn_model():
            print("\n" + "=" * 60)
            print("🎉 训练完成！")
            print("📁 模型文件保存在 output/ 目录中")
            print("📊 查看训练日志了解模型性能")
            print("=" * 60)
        else:
            print("\n❌ 训练失败，请检查错误信息")
    else:
        print("👋 程序退出")

if __name__ == "__main__":
    main()
