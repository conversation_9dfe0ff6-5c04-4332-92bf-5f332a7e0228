#!/usr/bin/env python3
"""
调试目标商品ID计算问题
"""

import torch
import numpy as np
from utils.data_loader import RecommendationDataset

def debug_target_item_calculation():
    """调试目标商品ID的计算逻辑"""
    print("=== 调试目标商品ID计算 ===")
    
    # 加载数据
    user_history_actions = np.load("./data/user_history_actions.npy", allow_pickle=True)
    user_history_actions = user_history_actions[:10]  # 只用10个样本
    
    dataset = RecommendationDataset(
        user_history_actions=user_history_actions,
        product_sample_num=100
    )
    
    print(f"最大商品ID: {dataset.emb_manager.max_item_id}")
    print(f"有效商品数量: {len(dataset.emb_manager.valid_product_ids)}")
    print(f"有效商品ID范围: {min(dataset.emb_manager.valid_product_ids)} - {max(dataset.emb_manager.valid_product_ids)}")
    
    # 检查前5个样本
    for i in range(5):
        print(f"\n--- 样本 {i} ---")

        # 获取原始数据
        user_data = user_history_actions[i]
        print(f"原始用户行为数据: {user_data}")

        # 从字典中提取目标商品ID
        target_item_id = user_data['item_id']
        if hasattr(target_item_id, 'item'):
            target_item_id = target_item_id.item()
        else:
            target_item_id = int(target_item_id)

        print(f"目标商品ID (原始): {target_item_id}")

        # 获取历史行为
        history_actions = user_data['history_actions']
        print(f"历史行为: {history_actions}")
        
        # 检查是否在有效商品列表中
        is_valid = target_item_id in dataset.emb_manager.valid_product_ids
        print(f"目标商品是否有效: {is_valid}")
        
        # 获取数据集样本
        sample = dataset[i]
        history_tensor, product_tensor, target_norm, label, candidate_pool = sample
        
        # 计算归一化后再反归一化的商品ID
        calculated_target_id = int(target_norm.item() * dataset.emb_manager.max_item_id)
        print(f"目标商品ID (计算): {calculated_target_id}")
        print(f"归一化值: {target_norm.item():.6f}")
        
        # 检查候选池
        print(f"候选池大小: {len(candidate_pool)}")
        print(f"候选池前5个: {candidate_pool[:5]}")
        print(f"目标商品在候选池: {target_item_id in candidate_pool}")
        print(f"计算目标商品在候选池: {calculated_target_id in candidate_pool}")
        
        # 如果不匹配，分析原因
        if target_item_id != calculated_target_id:
            print(f"❌ 目标商品ID不匹配!")
            print(f"  原始: {target_item_id}")
            print(f"  计算: {calculated_target_id}")
            print(f"  差异: {abs(target_item_id - calculated_target_id)}")
            
            # 检查归一化逻辑
            correct_norm = target_item_id / float(dataset.emb_manager.max_item_id)
            print(f"  正确归一化值: {correct_norm:.6f}")
            print(f"  实际归一化值: {target_norm.item():.6f}")
        else:
            print("✅ 目标商品ID匹配")

def debug_dataset_getitem():
    """调试数据集的__getitem__方法"""
    print("\n=== 调试数据集__getitem__方法 ===")

    user_history_actions = np.load("./data/user_history_actions.npy", allow_pickle=True)
    user_history_actions = user_history_actions[:5]

    dataset = RecommendationDataset(
        user_history_actions=user_history_actions,
        product_sample_num=100
    )

    for i in range(3):
        print(f"\n--- 样本 {i} 详细分析 ---")

        # 获取原始数据
        user_data = user_history_actions[i]
        print(f"用户数据: {user_data}")

        # 提取信息
        target_item_id = user_data['item_id']
        target_action = user_data['action']
        history_actions = user_data['history_actions']

        if hasattr(target_item_id, 'item'):
            target_item_id = target_item_id.item()
        else:
            target_item_id = int(target_item_id)

        print(f"目标: 商品ID={target_item_id}, 行为={target_action}")
        print(f"历史行为数量: {len(history_actions)}")

        # 分析历史行为
        for j, action in enumerate(history_actions):
            item_id = action[0]
            behavior = action[1] if len(action) > 1 else 'unknown'
            print(f"  历史行为 {j}: 商品ID={item_id}, 行为={behavior}")
        
        # 检查目标商品的有效性
        is_valid = target_item_id in dataset.emb_manager.valid_product_ids
        print(f"目标商品有效性: {is_valid}")
        
        if not is_valid:
            print(f"❌ 目标商品 {target_item_id} 不在有效商品列表中!")
            # 找到最接近的有效商品
            valid_ids = list(dataset.emb_manager.valid_product_ids)
            closest_id = min(valid_ids, key=lambda x: abs(x - target_item_id))
            print(f"最接近的有效商品ID: {closest_id}")
        
        # 获取数据集处理后的结果
        try:
            sample = dataset[i]
            history_tensor, product_tensor, target_norm, label, candidate_pool = sample
            
            calculated_target = int(target_norm.item() * dataset.emb_manager.max_item_id)
            print(f"数据集处理后的目标商品ID: {calculated_target}")
            print(f"目标商品在候选池第一位: {candidate_pool[0] == target_item_id}")
            
        except Exception as e:
            print(f"❌ 数据集处理出错: {e}")

def main():
    """主函数"""
    print("开始调试目标商品ID计算问题...")
    
    try:
        debug_target_item_calculation()
        debug_dataset_getitem()
        
        print("\n=== 调试总结 ===")
        print("请检查以上输出，重点关注:")
        print("1. 目标商品ID是否在有效商品列表中")
        print("2. 归一化和反归一化计算是否正确")
        print("3. 候选池构建是否包含目标商品")
        
    except Exception as e:
        print(f"调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
